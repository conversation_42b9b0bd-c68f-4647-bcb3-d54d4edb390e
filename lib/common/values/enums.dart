enum HeroTagName {
  ///用户自己头像
  profileAvatar,

  ///用户自己的名字
  profileName,

  ///用户自己的id
  profileId,

  ///聊天里的图片
  chatMsgImg,

  ///标签图片
  labelImg,

  ///卡盒图片
  cardImg,

  ///动态头像
  postAvatar,

  ///动态图片
  postImg,

  ///动态视频
  postVideo,

  ///存甜罐封面
  sweetBottleCover,

  ///水晶球
  crystalBall,
  ;

  ///如果某个标志有多个，需要对其一一区分，例如聊天列表有多个图片，需要区分是哪个的时候
  String of(Object id) {
    return '${name}_$id';
  }
}

///动态帖子类型
enum PostType {
  unknown,
  text,
  image,
  video,
  audio,
}

///动态列表类型
enum DynamicType {
  unknown,
  recommend,
  friend,
  mine,
  sameCity,
}

///通讯录分组类型
enum ContactsGroupType {
  unknown,
  friend,
  dazi,
  chatGroup,
  blacklist,
  newFriend,
}

///搭子关系类型
enum UserRelationType {
  friend,
  pureDa, //纯搭
  simpleDa, //浅搭
  moreDa, //浅搭随缘
  closeDa, //深搭
}

///聊天室类型
enum ChatRoomType {
  unknown,
  fair, //市集
  teapot, //茶壶
  sleep; //睡前卧谈会
}

enum PropType {
  all, //全部
  daCoin, //1.搭币
  daBang, //2.搭棒
  dyePowder, //3.染色剂粉末
  stains, //4.染色剂
  skinChip, //5.皮肤碎片
  globalSpeaker, //6.全域喇叭
  avatarFrame, //7.头像框
  chatBox, //8.聊天框
  worldChat, //9.世界聊天
  monthCard, //10.月卡
  womenRoleImage, //11.女角色形象
  manRoleImage, //12.男角色形象
  changeSexCard, //13.变性卡
  other1, //14.买一赠一
  other2, //15.其他
  idCard, //16.靓号转换卡
  treasureBox, //17.宝箱
  boxKey, //18.宝箱钥匙
  ;

  static PropType fromInt(int value) {
    if (value >= 0 && value < PropType.values.length) {
      return PropType.values[value];
    }
    throw ArgumentError('Invalid index for PropType: $value');
  }
}

enum PayType {
  wechat,
  alipay,
  apple,
}

enum PayResultStatus {
  success,
  failed,
}

///装扮类型
enum DressUpType {
  avatar,
  chatBox, //聊天气泡
}

const String kUserRoleImageStatusBack = "BACK";
const String kUserRoleImageStatusFront = "FRONT";
const String kUserRoleImageStatusListenMusic = "LISTEN_MUSIC";
const String kUserRoleImageStatusLookBook = "LOOK_BOOK";
const String kUserRoleImageStatusPlayComputer = "PLAY_COMPUTER";
const String kUserRoleImageStatusSitPlayPhone = "SIT_PLAY_PHONE";

enum UserRoleImageStatus {
  back(kUserRoleImageStatusBack),
  front(kUserRoleImageStatusFront),
  listenMusic(kUserRoleImageStatusListenMusic),
  lookBook(kUserRoleImageStatusLookBook),
  playComputer(kUserRoleImageStatusPlayComputer),
  sitPlayPhone(kUserRoleImageStatusSitPlayPhone),
  ;

  final String value;

  const UserRoleImageStatus(this.value);
}

///举报类型
enum ReportType {
  unknown,
  chatMsg,
  user,
  group,
  team,
  post,
  roomFair,
  roomTeapot,
  ;

  static ReportType fromInt(int value) {
    if (value >= 0 && value < ReportType.values.length) {
      return ReportType.values[value];
    }
    throw ArgumentError('Invalid index for ReportType: $value');
  }
}

enum TodoTogetherListType {
  history,
  all,
}
