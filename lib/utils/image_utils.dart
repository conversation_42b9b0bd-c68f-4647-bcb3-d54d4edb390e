import 'dart:io';

import 'package:cached_network_image/cached_network_image.dart';
import 'package:dada/common/values/enums.dart';
import 'package:dada/components/widgets/image_browser.dart';
import 'package:dada/components/widgets/video_fullscreen_play.dart';
import 'package:dada/services/im/chat_im_manager.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:svgaplayer_flutter/player.dart';
import 'package:tencent_cloud_chat_sdk/models/v2_tim_user_full_info.dart';
import 'package:video_player/video_player.dart';
import 'package:flutter_cache_manager/flutter_cache_manager.dart';

class ImageUtils {
  /// 校验url类型根据类型返回对应的组件
  ImageUtils._();

  static const defaultPlaceholder = 'assets/images/avatar_placeholder.webp';

  static DefaultCacheManager defaultCacheManager = DefaultCacheManager();

  static Widget getImage(String url, double? width, double? height,
      {String? placeholder,
      String? errorholder,
      String? format,
      BoxFit? fit,
      Color? color,
      double? radius,
      Color? borderColor,
      bool? showBorder,
      bool? showPlaceholder}) {
    if (url.isEmpty && showPlaceholder == null) {
      return const SizedBox();
    }
    if (url.startsWith("http://") || url.startsWith("https://")) {
      // 网络图片
      return getNetImage(
        url,
        width: width,
        height: height,
        placeholder: placeholder,
        errorholder: errorholder,
        format: format,
        fit: fit,
        color: color,
        circular: radius,
        showBorder: showBorder,
        showPlaceholder: showPlaceholder,
      );
    } else if (url.startsWith("/") || url.startsWith("file://")) {
      // 本地图片文件
      return getFileImage(url,
          width: width,
          height: height,
          fit: fit,
          color: color,
          circular: radius);
    } else {
      // 静态资源图片
      return getAssetImage(
        url,
        width: width,
        height: height,
        format: format,
        fit: fit,
        color: color,
        placeholder: placeholder,
        showPlaceholder: showPlaceholder,
        circular: radius,
      );
    }
  }

  /// 获取本地文件中的图片。
  static Widget getFileImage(String url,
      {double? width,
      double? height,
      BoxFit? fit,
      Color? color,
      double? circular}) {
    return Container(
      width: width,
      height: height,
      decoration: BoxDecoration(
        borderRadius: circular != null ? BorderRadius.circular(circular) : null,
        image: DecorationImage(
          image: FileImage(File(url)),
          fit: fit,
        ),
      ),
    );
  }

  /// 获取网络中的图片。
  static Widget getNetImage(String imageUrl,
      {double? width,
      double? height,
      String? placeholder,
      String? errorholder,
      String? format,
      BoxFit? fit = BoxFit.cover,
      Color? color,
      double? circular,
      Color? borderColor,
      bool? showBorder,
      bool? showPlaceholder}) {
    return build(
      imageUrl,
      width: width,
      height: height,
      placeholder: placeholder,
      error: errorholder,
      fit: fit,
      radius: circular,
      borderColor: borderColor,
      showBorder: showBorder ?? false,
      showPlaceholder: showPlaceholder ?? false,
    );
  }

  ///加载网络图片方法
  static Widget build(String url,
      {double? width,
      double? height,
      double? radius,
      BoxFit? fit,
      String? placeholder,
      Color? borderColor,
      bool showBorder = false,
      bool showPlaceholder = false,
      String? error,
      bool isProgress = false,
      Color? color}) {
    final placeholderWidget = showPlaceholder
        ? (radius != null
            ? Container(
                width: width,
                height: height,
                decoration: BoxDecoration(
                  color: color,
                  borderRadius: BorderRadius.circular(radius),
                  border: showBorder == true
                      ? Border.all(color: borderColor ?? Colors.white, width: 1)
                      : null,
                ),
                child: color == null
                    ? Image.asset(getImgPath(placeholder ?? defaultPlaceholder))
                    : Container(),
              )
            : isProgress
                ? const Center(
                    child: CircularProgressIndicator(),
                  )
                : Container(
                    width: width,
                    height: height,
                    color: color,
                    child: color == null
                        ? Image.asset(
                            getImgPath(placeholder ?? defaultPlaceholder))
                        : Container(),
                  ))
        : const SizedBox();
    if (url.isEmpty) {
      return placeholderWidget;
    }
    return CachedNetworkImage(
      cacheManager: defaultCacheManager,
      height: height,
      width: width,
      imageUrl: url,
      fit: fit,
      // progressIndicatorBuilder: isProgress ? (context ,_ , progress) => const Center(child: CircularProgressIndicator(),) : null,
      placeholder: (context, url) => placeholderWidget,
      errorWidget: (context, url, er) => placeholderWidget,
      imageBuilder: radius != null
          ? (context, imageProvider) => Container(
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(radius),
                  border: showBorder == true
                      ? Border.all(color: borderColor ?? Colors.white, width: 1)
                      : null,
                  image: DecorationImage(
                    image: imageProvider,
                    fit: fit ?? BoxFit.cover,
                  ),
                ),
              )
          : null,
    );
  }

  /// 获取资源Image.asset中的图片。
  static Widget getAssetImage(String imgId,
      {double? width,
      double? height,
      String? format,
      BoxFit? fit,
      Color? color,
      String? placeholder,
      double? circular,
      bool? showPlaceholder,
      bool? showBorder,
      Color? borderColor,
      BorderRadiusGeometry? borderRadius}) {
    if (!imgId.startsWith('assets/')) {
      return Container(
        height: height,
        width: width,
        decoration: BoxDecoration(
          color: color,
          borderRadius: borderRadius ?? BorderRadius.circular(circular ?? 0),
          border: showBorder == true
              ? Border.all(
                  color: borderColor ?? Colors.white,
                  width: 1,
                )
              : null,
        ),
        child: showPlaceholder == true
            ? Image.asset(
                getImgPath(placeholder ?? defaultPlaceholder),
              )
            : null,
      );
    }
    return Container(
      clipBehavior: Clip.hardEdge,
      decoration: BoxDecoration(
        borderRadius: borderRadius ?? BorderRadius.circular(circular ?? 0),
        border: showBorder == true ? Border.all(
          color: borderColor ?? Colors.white,
          width: 1,
        ) : null,
      ),
      child: Image.asset(
        getImgPath(
            imgId.trim().isNotEmpty
                ? imgId
                : (showPlaceholder == true
                    ? placeholder ?? defaultPlaceholder
                    : ""),
            format: format),
        width: width,
        height: height,
        fit: fit,
        color: color,
        excludeFromSemantics: true,
        //去除图片语义
        gaplessPlayback: true, //重新加载图片的过程中，原图片的展示是否保留
      ),
    );
  }

  ///带头像框
  static Widget getFrameAvatar(String imageUrl,
      {double? width, double? height, String? frameUrl, double space = 20}) {
    return Stack(
      alignment: Alignment.center,
      children: [
        Positioned(
          child:
              getNetImage(imageUrl, width: width, height: height, circular: 99),
        ),
        if ((frameUrl ?? "").isNotEmpty)
          SizedBox(
            width: width != null ? (width + space) : width,
            height: height != null ? (height + space) : height,
            child: SVGASimpleImage(resUrl: frameUrl),
          ),
      ],
    );
  }

  ///assets资源路径。
  static String getImgPath(String name, {String? format}) {
    if (name.contains('.')) {
      return name;
    } else {
      return '$name${format ?? '.webp'}';
    }
  }

  ///展示图片预览（包含hero动画）
  static showImageBrowser(ImageBrowserArgs args) {
    ///在当前页面显示，不进行页面Push动画
    Navigator.push(Get.context!, PageRouteBuilder(pageBuilder:
        (BuildContext context, Animation<double> animation,
            Animation secondaryAnimation) {
      return FadeTransition(
        opacity: animation,
        child: ImageBrowser(args: args),
      );
    }));
  }

  ///全屏展示视频播放
  static showVideoPlayer(
      {VideoPlayerController? controller, String? videoUrl, String? heroTag}) {
    ///在当前页面显示，不进行页面Push动画
    Navigator.push(Get.context!, PageRouteBuilder(pageBuilder:
        (BuildContext context, Animation<double> animation,
            Animation secondaryAnimation) {
      return FadeTransition(
        opacity: animation,
        child: VideoFullScreenPlayPage(
          videoPlayerController: controller,
          videoUrl: videoUrl,
          heroTag: heroTag,
        ),
      );
    }));
  }

  static Future<String?> getUserDressUpUrl(
      String userID, DressUpType type) async {
    V2TimUserFullInfo? userInfo =
        await ChatIMManager.sharedInstance.getUserInfo(userID);
    if (type == DressUpType.avatar) {
      return userInfo?.customInfo?["avatar"];
    } else if (type == DressUpType.chatBox) {
      return userInfo?.customInfo?["dialog"];
    }
    return null;
  }
}
