enum Env {
  ///线上环境
  prod,

  ///开发环境
  dev
}

class ApiConfig {
  static Env env = Env.dev;

  /// 基础应用服务
  static String get baseUrl {
    return const String.fromEnvironment("baseUrl");
  }

  ///web url 服务
  static String get webBaseUrl {
    return const String.fromEnvironment("webBaseUrl");
  }

  static const timeout = 5000;
  static const successCode = 0;
  static const tokenExpired = 10021;
  static const accountBanned = 10030; //账号被封禁
}

///所有的api 接口都放在这，便于统一管理
class Api {
  ///获取短信验证码
  static const getSmsCode = "/login/sendCode";

  ///短信验证码登录
  static const loginWithCode = "/login/login";

  ///退出登录
  static const logout = "/login/logout";

  ///注册
  static const register = "/login/register";

  ///上传文件
  static const uploadFile = "/upload";

  ///获取随机昵称
  static const getRandomNickname = "/login/nickName";

  ///用户信息
  static const userInfo = "/user/userinfo";

  ///更新用户信息
  static const updateUser = "/user/updateUser";

  ///添加用户标签
  static const addUserLabel = "/user/label";

  ///删除用户标签
  static const deleteUserLabel = "/user/label/delete";

  ///添加用户卡片
  static const addUserCard = "/user/card";

  ///删除用户卡片
  static const deleteUserCard = "/user/card/delete";

  ///获取用户卡片列表
  static const userCardList = "/user/card/info";

  ///用户卡片（点赞、已阅、懂）
  static const userCardOperation = "/user/card/operation";

  ///获取用户签到信息
  static const userSignInData = "/sign/list";

  ///用户签到
  static const signIn = "/sign";

  ///获取动态列表
  static const dynamicList = "/post/page";

  ///发布动态
  static const sendPost = "/post/save";

  ///动态详情
  static const postDetail = "/post/getPost";

  ///动态点赞
  static const postLike = "/post/like";

  ///取消点赞
  static const postUnLike = "/post/noLike";

  ///动态评论列表
  static const postCommentList = "/post/info";

  ///评论回复列表
  static const commentReplyList = "/post/replay";

  ///动态置顶
  static const postDelete = "/post/delete";

  ///动态删除
  static const postTop = "/post/isTop";

  ///动态仅自己可见
  static const postHide = "/post/hide";

  ///发布动态评论
  static const postComment = "/post/comment";

  ///发布评论回复
  static const commentReply = "/post/commentReply";

  ///动态消息
  static const dynamicMessage = "/post/msg";

  ///添加动态限制地区
  static const postRegionLimitAdd = "/userpostlimitregion/save";

  ///查询动态限制地区
  static const postRegionLimitList = "/userpostlimitregion/list";

  ///删除动态限制地区
  static const postRegionLimitDelete = "/userpostlimitregion/delete";

  ///匹配搭子
  static const dadaMatch = "/dadaMatch/matching";

  ///匹配小队
  static const matchTeam = "/dadaMatch/matchTeam";

  ///创建小队
  static const createTeam = "/dadaMatch/createTeam";

  ///开始了解
  static const startKnow = "/dadaMatch/toDada";

  ///搭子申请
  static const applyDazi = "/dadaRoom/applyDaZi";

  ///集结处大厅
  static const assembleHall = "/dadaMatch/teamList";

  ///搜索小队
  static const searchTeam = "/dadaMatch/searchTeam";

  ///加入小队
  static const joinTeam = "/dadaMatch/joinTeam";

  ///小队详情
  static const teamInfo = "/dadaMatch/teamInfo";

  ///小队 上锁/解锁
  static const teamLock = "/dadaMatch/teamLock";

  ///小队信息修改
  static const teamUpdate = "/dadaMatch/updateTeam";

  ///退出小队
  static const exitTeam = "/dadaMatch/exitTeam";

  ///踢出小队
  static const kickTeamMember = "/dadaMatch/kickTeamMember";

  ///邀请人加入小队
  static const inviteTeam = "/dadaMatch/inviteTeam";

  ///保存语音到语音盒子
  static const saveAudioToBox = "/video/box/save";

  ///从语音盒子删除语音
  static const deleteAudioToBox = "/video/box/delete";

  ///获取通讯录列表
  static const contactsList = "/friend/groupFriendList";

  ///添加好友
  static const addFriend = "/friend/addFriend";

  ///删除好友
  static const deleteFriend = "/friend/deleteFriend";

  ///修改好友信息
  static const updateFriendInfo = "/friend/updateFriend";

  ///获取好友信息详情
  static const friendInfo = "/friend/getFriendInfo";

  ///同意好友申请
  static const agreeFriendApply = "/friend/agree";

  ///同意搭子申请
  static const agreeDaziApply = "/dadaRoom/daState";

  ///好友自定义分组列表
  static const friendCustomGroupList = "/group/userList";

  ///好友自定义分组列表信息修改
  static const updateFriendCustomGroupInfo = "/group";

  ///添加好友自定义分组
  static const addFriendCustomGroup = "/group";

  ///删除好友自定义分组
  static const deleteFriendCustomGroup = "/group";

  ///好友自定义分组列表排序
  static const sortFriendCustomGroupList = "/group/sort";

  ///搜索全部 好友/群/陌生人
  static const searchAllUsersAndGroups = "/queryHistory/searchFriend";

  ///获取群聊信息详情
  static const getGroupInfo = "/chats";

  ///创建群聊
  static const createChatGroup = "/chats/saveUserCrowdChats";

  ///修改群聊信息
  static const updateGroupInfo = "/chats/updateUserCrowdChats";

  ///删除群聊
  static const deleteGroup = "/chats/deleteUserCrowdChats";

  ///解散群聊
  static const disbandGroup = "/chats/deleteUserCrowdChats";

  ///加入黑名单
  static const addToBlackList = "/blacklist/blackListAdd";

  ///移出黑名单
  static const deleteFromBlackList = "/blacklist/blackListDelete";

  ///获取黑名单列表
  static const blackList = "/blacklist/blackListGet";

  ///添加群标签
  static const addGroupLabel = "/usercrowdchatstag";

  ///删除标签
  static const deleteGroupLabel = "/usercrowdchatstag";

  ///存甜罐数量
  static const sweetTotal = "/chat/sweetTotal";

  ///存甜罐列表
  static const sweetList = "/chat/sweetList";

  ///新增存甜罐
  static const insertSweet = "/chat/insertSweet";

  ///删除存甜罐
  static const deleteSweet = "/chat/deleteSweet";

  ///邀请去玩列表
  static const invitePlayList = "/inviteplay/queryInvitePlayList";

  ///新增邀请去玩
  static const addInvitePlay = "/inviteplay/saveInvitePlay";

  ///修改邀请去玩
  static const updateInvitePlay = "/inviteplay/updateInvitePlay";

  ///删除邀请去玩
  static const deleteInvitePlay = "/inviteplay/deleteInvitePlay";

  ///搜索历史
  static const searchHistoryList = "/queryHistory/searchFriendHistory";

  ///删除搜索历史
  static const deleteSearchHistoryList = "/queryHistory/deleteSearchHistory";

  ///获取小屋信息
  static const roomInfo = "/dadaRoom/info";

  ///修改小屋信息
  static const updateRoomInfo = "/dadaRoom/updateRoom";

  ///添加气泡语
  static const addRoomBubbleWord = "/dadaRoom/bubble";

  ///删除气泡语
  static const deleteRoomBubbleWord = "/dadaRoom/deleteBubble";

  ///邮件内消息列表查询
  static const getMailMessageList = "/dadaRoom/roomMsg";

  ///小屋内添加弹幕
  static const addRoomBarrage = "/dadaRoom/insertRoomMsg";

  ///小屋内删除弹幕
  static const deleteRoomBarrage = "/dadaRoom/deleteRoomMsg";

  ///设置搭子关系
  static const updateDaziRelation = "/dadaRoom/friendSet";

  ///设置搭子申请状态
  static const updateRoomDaziApplyState = "/dadaRoom/isAccept";

  ///请出小屋
  static const kickOutUserFromSmallRoom = "/dadaRoom/leaveRoom";

  ///任务列表
  static const roomTaskList = "/dadaRoomTask/list";

  ///（市集、茶壶）房间详细信息
  static const chatRoomInfo = "/room/roomInfo";

  ///创建（市集、茶壶）房间
  static const createChatRoom = "/room/saveRoom";

  ///修改房间信息
  static const updateChatRoom = "/room/updateRoomName";

  ///删除房间
  static const deleteChatRoom = "/room/deleteRoom";

  ///房间列表
  static const chatRoomList = "/room/list";

  ///匹配房间
  static const matchChatRoom = "/room/randomMatchingRoom";

  ///随机生成房间内用户角色形象信息
  static const getRandomRoomUserRoleInfo = "/room/randomUserNickInfo";

  ///房间麦位信息
  static const chatRoomSeatList = "/room/getSeatInfo";

  ///设置房间自定义属性信息
  static const setChatRoomAttributes = "/room/wheatPositionChange";

  ///转让房主
  static const chatRoomChangeOwner = "/room/changeGroupOwner";

  ///更新房间话题
  static const updateRoomTopic = "/roomtopic/updateRoomtopic";

  ///使用道具、角色换装等（赠送搭棒、换装）
  static const useProp = "/prop/usePorp";

  ///搜索房间或话题关键字
  static const searchRoom = "/queryHistory/searchHome";

  ///搜索首页历史列表
  static const searchRoomHistoryList = "/queryHistory/searchHomeHistory";

  ///话题提名
  static const topicNominate = "/roomtopic/topicBooster";

  ///账户明细
  static const billDetail = "/prop/billDetail";

  ///物品商城/兑换商城
  static const storeList = "/prop/shopList";

  ///删除首页房间搜索历史
  static const deleteHomeSearchList = "/queryHistory/deleteSearchHomeHistory";

  ///查询背包道具和角色形象装扮
  static const getBackpackPropList = "/package/queryPackageOrRole";

  ///查询用户道具余额（搭棒、搭币、染色剂等数量）
  static const getUserAccountBalance = "/package/queryPropNum";

  ///收藏或取消收藏道具
  static const collectProp = "/package/favoritesProp";

  ///赠送道具
  static const sendProp = "/package/giftProp";

  ///获取共鸣动态列表
  static const dynamicResonanceList = "/postresonate/queryPostResonateList";

  ///获取动态共鸣的列表
  static const dynamicResonancePostList =
      "/postresonate/queryPostResonateDetails";

  ///批量删除共鸣数据
  static const deleteResonancePost = "/postresonate/deleteBatch";

  ///获取搭圈消息未读数
  static const getDynamicMsgUnreadCount = "/post/notReadMsg";

  ///获取语音盒子列表
  static const audioBoxList = "/video/box/list";

  ///分享搭圈动态成功调一下接口
  static const sharePostSuccess = "/post/sharePost";

  ///提交填写邀请码
  static const submitInvitationCode = "/userinvite/submitInvitationCode";

  ///换绑手机号
  static const changePhone = "/login/changePhone";

  ///实名认证
  static const realAuth = "/user/id2MetaVerify";

  ///查询用户是否接收陌生人消息
  static const getStrangerMsgState = "/user/strangerState";

  ///设置用户是否接收陌生人消息
  static const updateStrangerMsgState = "/user/updateStrangerState";

  ///创建订单
  static const createOrder = "/dadaorder/createOrder";

  ///充值列表
  static const rechargeList = "/dadagoods/queryGoodsList";

  ///探索信息
  static const getExploreInfo = "/explore/info";

  ///开始探索
  static const startExplore = "/explore/startExplore";

  ///收获探索
  static const getExplore = "/explore/getPrize";

  ///验证苹果支付
  static const applePayVerify = "/dadaorder/iosPayVerify";

  ///邀请用户列表
  static const getInviteList = "/userinvite/queryInviteRecord";

  ///注销
  static const logOff = "/user/cancelAccount";

  ///做任务
  static const doTask = "/dadaRoomTask/doTask";

  ///衣服转换款式（男款转女款，女款转男款）
  static const clothConvert = "/package/clothesDenaturing";

  ///水晶球记录
  static const crystalBallRecord = "/dadaRoom/listLife";

  ///水晶球记录详情
  static const crystalBallRecordDetail = "/dadaRoom/lifeDetail";

  ///水晶球添加记录
  static const crystalBallAddRecord = "/dadaRoom/recordLife";

  ///任务列表完成任务领取奖励
  static const getTaskReward = "/dadaRoomTask/getPrize";

  ///头像列表
  static const getAvatarList = "/user/avatarList";

  ///兑换头像
  static const bugAvatar = "/user/buyAvatar";

  ///获取共鸣未读数
  static const getPostResonateUnreadCount = "/postresonate/notReadMsg";

  ///获取是否设置密码状态
  static const isSetPassword = "/user/isSetPassword";

  ///设置密码
  static const setPassword = "/login/setPassword";

  ///设置密码（需要登录）
  static const passwordSet = "/login/passwordSet";

  ///修改密码
  static const changePassword = "/user/changePassword";

  ///找回共鸣
  static const backPostResonate = "/postresonate/backPostResonate";

  ///兑换道具
  static const buyProp = "/prop/buy";

  ///获取用户卡盒内卡片数量
  static const userCardNum = "/user/getCardCount";

  ///更新用户搭搭号
  static const updateDadaNo = "/user/updateDadaNo";

  ///离开他人小屋
  static const leaveSmallRoom = "/dadaRoom/outRoom";

  ///解除搭子关系
  static const relieveDadaRelation = "/dadaRoom/deleteDaZi";

  ///小屋信箱未读数
  static const getSmallRoomMsgUnreadCount = "/dadaRoom/noReadMsg";

  ///提交填写邀请码
  static const receiptCdk = "/dadacdk/receiptCdk";

  ///新注册用户发消息打招呼
  static const sendWelcomeGreeting = "/login/sendWelcomeGreeting";

  ///校验验证码
  static const checkCode = "/login/checkCode";

  ///举报
  static const report = "/report";

  ///App内一些设置开关接口
  static const systemConfig = "/sysparams/list";

  ///青少年模式开关
  static const teenModeStatus = "/dadateenager/queryIsOpenTeenager";

  ///打开/关闭青少年模式
  static const changeTeenModeStatus = "/dadateenager/toggleTeenager";

  ///首页banner
  static const bannerList = "/dadabanner/queryBanner";

  ///小屋戳一下
  static const smallRoomPoke = "/dadaRoom/poke";

  ///获取小屋访客列表
  static const seeMeList = "/user/visitList";

  ///获取一起做件事列表
  static const todoTogetherList = "/userTask/list";

  ///一起做件事发布
  static const todoTogetherPublish = "/userTask/publishTask";

  ///一起做件事删除
  static const todoTogetherDelete = "/userTask/delete";

  ///一起做件事开始
  static const todoTogetherStart = "/userTask/start";

  ///一起做件事开始任务前的图片
  static const todoTogetherStartImg = "/userTask/startImg";

  ///一起做件事查看对方的开始任务图片
  static const todoTogetherStartImgOther = "/userTask/selectStartImg";

  ///一起做件事终止任务
  static const todoTogetherStop = "/userTask/stopTask";

  ///一起做件事戳戳他
  static const todoTogetherPoke = "/userTask/chuo";
}
