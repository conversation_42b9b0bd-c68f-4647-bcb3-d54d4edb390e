import 'package:event_bus/event_bus.dart';

enum BusEvent {
  dynamicTabRefresh,
  userInfoUpdate,
  smallRoomMailHasNewMsg,
  smallRoomUpdateBarrage,
  refreshChatHistoryList,
  reloadChatDetail,
  receiveGroupApplication;
}

class EventBusEngine {
  static EventBus eventBus = EventBus();

  static fire({required BusEvent event, String? ext}) {
    eventBus.fire(EventEntity(event: event, message: ext));
  }

  static remove() {
    eventBus.destroy();
  }
}

class EventEntity {
  BusEvent event;
  String? message;

  EventEntity({required this.event, this.message});
}
