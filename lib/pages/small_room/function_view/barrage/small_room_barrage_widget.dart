import 'package:dada/common/values/text_styles.dart';
import 'package:dada/model/small_room_barrage_item_entity.dart';
import 'package:dada/services/event_bus/event_bus_engine.dart';
import 'package:dada/services/user/user_service.dart';
import 'package:dada/utils/image_utils.dart';
import 'package:flutter/material.dart';
import 'package:flutter_infinite_marquee/flutter_infinite_marquee.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

class SmallRoomBarrageWidget extends StatefulWidget {
  final List<SmallRoomBarrageItemEntity>? barrageList;

  const SmallRoomBarrageWidget({super.key, required this.barrageList});

  @override
  State<SmallRoomBarrageWidget> createState() => SmallRoomBarrageWidgetState();
}

class SmallRoomBarrageWidgetState extends State<SmallRoomBarrageWidget> {
  final List<SmallRoomBarrageItemEntity> _loopBarrageList = [];
  int currentIndex = 0;

  @override
  void initState() {
    super.initState();

    EventBusEngine.eventBus.on().listen((event) {
      EventEntity entity = event;
      if (entity.event == BusEvent.smallRoomUpdateBarrage) {
        SmallRoomBarrageItemEntity barrage = SmallRoomBarrageItemEntity();
        barrage.content = entity.message;
        barrage.avatar = UserService().user?.avatar;
        _loopBarrageList.insert(0, barrage);
      }
    });

    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (widget.barrageList != null && _loopBarrageList.isEmpty) {
        Future.delayed(const Duration(seconds: 1), () {
          if (widget.barrageList!.length > 20) {
            _loopBarrageList.addAll(widget.barrageList!.sublist(0, 20));
          } else {
            _loopBarrageList.addAll(widget.barrageList!);
          }
        });
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: EdgeInsets.only(top: 10.h),
      width: ScreenUtil().screenWidth,
      height: 20.h,
      child: InfiniteMarquee(
        initialScrollOffset: -ScreenUtil().screenWidth,
        itemBuilder: (context, index) {
          if (index < 0 || _loopBarrageList.isEmpty) {
            return Container(
                width: 50.w, height: 20.h, color: Colors.transparent);
          }
          if (currentIndex >= _loopBarrageList.length) {
            currentIndex = 0;
            return Container(
                width: ScreenUtil().screenWidth * 2,
                height: 20.h,
                color: Colors.transparent);
          }
          SmallRoomBarrageItemEntity barrage = _loopBarrageList[currentIndex];
          currentIndex++;
          return SmallRoomBarrageItemWidget(
            barrageItem: barrage,
          );
        },
      ),
    );
  }

  void addBarrage(SmallRoomBarrageItemEntity barrage) {
    _loopBarrageList.insert(0, barrage);
    setState(() {});
  }
}

class SmallRoomBarrageItemWidget extends StatelessWidget {
  final SmallRoomBarrageItemEntity barrageItem;
  final Function()? onDelete;

  const SmallRoomBarrageItemWidget(
      {super.key, required this.barrageItem, this.onDelete});

  @override
  Widget build(BuildContext context) {
    RxBool visible = true.obs;
    return Obx(() {
      return Visibility(
        visible: visible.value,
        child: Container(
          height: 20.h,
          margin: EdgeInsets.only(right: 10.w),
          padding: EdgeInsets.only(right: 10.w),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(10.r),
            color: Colors.black.withOpacity(0.5),
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Padding(
                padding: EdgeInsets.only(left: 2.w),
                child: ClipOval(
                  child: ImageUtils.getImage(
                      barrageItem.avatar ?? "", 16.w, 16.w,
                      fit: BoxFit.cover),
                ),
              ),
              Padding(
                padding: EdgeInsets.only(left: 5.w),
                child: Text(
                  "${barrageItem.nickname ?? ""}：${barrageItem.content ?? ""}",
                  style: TextStyles.common(12.sp, Colors.white),
                ),
              ),
            ],
          ),
        ),
      );
    });
  }
}
