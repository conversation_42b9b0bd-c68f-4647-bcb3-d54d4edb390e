import 'package:dada/common/values/text_styles.dart';
import 'package:dada/model/small_room_barrage_item_entity.dart';
import 'package:dada/services/event_bus/event_bus_engine.dart';
import 'package:dada/services/user/user_service.dart';
import 'package:dada/utils/image_utils.dart';
import 'package:flutter/material.dart';
import 'package:flutter_infinite_marquee/flutter_infinite_marquee.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

class SmallRoomBarrageWidget extends StatefulWidget {
  final List<SmallRoomBarrageItemEntity>? barrageList;

  const SmallRoomBarrageWidget({super.key, required this.barrageList});

  @override
  State<SmallRoomBarrageWidget> createState() => SmallRoomBarrageWidgetState();
}

class SmallRoomBarrageWidgetState extends State<SmallRoomBarrageWidget> {
  final List<SmallRoomBarrageItemEntity> _loopBarrageList1 = [];
  final List<SmallRoomBarrageItemEntity> _loopBarrageList2 = [];
  int currentIndex1 = 0;
  int currentIndex2 = 0;

  @override
  void initState() {
    super.initState();

    EventBusEngine.eventBus.on().listen((event) {
      EventEntity entity = event;
      if (entity.event == BusEvent.smallRoomUpdateBarrage) {
        SmallRoomBarrageItemEntity barrage = SmallRoomBarrageItemEntity();
        barrage.content = entity.message;
        barrage.avatar = UserService().user?.avatar;
        // 交替插入两行
        if (_loopBarrageList1.length <= _loopBarrageList2.length) {
          _loopBarrageList1.insert(0, barrage);
        } else {
          _loopBarrageList2.insert(0, barrage);
        }
        setState(() {});
      }
    });

    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (widget.barrageList != null &&
          _loopBarrageList1.isEmpty &&
          _loopBarrageList2.isEmpty) {
        Future.delayed(const Duration(seconds: 1), () {
          List<SmallRoomBarrageItemEntity> list;
          if (widget.barrageList!.length > 20) {
            list = widget.barrageList!.sublist(0, 20);
          } else {
            list = widget.barrageList!;
          }
          // 平分到两行
          for (int i = 0; i < list.length; i++) {
            if (i % 2 == 0) {
              _loopBarrageList1.add(list[i]);
            } else {
              _loopBarrageList2.add(list[i]);
            }
          }
          setState(() {});
        });
      }
    });
  }

  @override
  void didUpdateWidget(covariant SmallRoomBarrageWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.barrageList != oldWidget.barrageList) {
      setState(() {
        _loopBarrageList1.clear();
        _loopBarrageList2.clear();
        List<SmallRoomBarrageItemEntity> list;
        if (widget.barrageList!.length > 20) {
          list = widget.barrageList!.sublist(0, 20);
        } else {
          list = widget.barrageList!;
        }
        for (int i = 0; i < list.length; i++) {
          if (i % 2 == 0) {
            _loopBarrageList1.add(list[i]);
          } else {
            _loopBarrageList2.add(list[i]);
          }
        }
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Container(
          margin: EdgeInsets.only(top: 10.h),
          width: ScreenUtil().screenWidth,
          height: 20.h,
          child: InfiniteMarquee(
            initialScrollOffset: -ScreenUtil().screenWidth,
            itemBuilder: (context, index) {
              if (index < 0 || _loopBarrageList1.isEmpty) {
                return Container(
                    width: 50.w, height: 20.h, color: Colors.transparent);
              }
              if (currentIndex1 >= _loopBarrageList1.length) {
                currentIndex1 = 0;
                return Container(
                    width: ScreenUtil().screenWidth * 2,
                    height: 20.h,
                    color: Colors.transparent);
              }
              SmallRoomBarrageItemEntity barrage = _loopBarrageList1[currentIndex1];
              currentIndex1++;
              return SmallRoomBarrageItemWidget(
                barrageItem: barrage,
              );
            },
          ),
        ),
        Container(
          margin: EdgeInsets.only(top: 4.h),
          width: ScreenUtil().screenWidth,
          height: 20.h,
          child: InfiniteMarquee(
            initialScrollOffset: -ScreenUtil().screenWidth,
            itemBuilder: (context, index) {
              if (index < 0 || _loopBarrageList2.isEmpty) {
                return Container(
                    width: 50.w, height: 20.h, color: Colors.transparent);
              }
              if (currentIndex2 >= _loopBarrageList2.length) {
                currentIndex2 = 0;
                return Container(
                    width: ScreenUtil().screenWidth * 2,
                    height: 20.h,
                    color: Colors.transparent);
              }
              SmallRoomBarrageItemEntity barrage = _loopBarrageList2[currentIndex2];
              currentIndex2++;
              return SmallRoomBarrageItemWidget(
                barrageItem: barrage,
              );
            },
          ),
        ),
      ],
    );
  }

  void addBarrage(SmallRoomBarrageItemEntity barrage) {
    if (_loopBarrageList1.length <= _loopBarrageList2.length) {
      _loopBarrageList1.insert(0, barrage);
    } else {
      _loopBarrageList2.insert(0, barrage);
    }
    setState(() {});
  }
}

class SmallRoomBarrageItemWidget extends StatelessWidget {
  final SmallRoomBarrageItemEntity barrageItem;
  final Function()? onDelete;

  const SmallRoomBarrageItemWidget(
      {super.key, required this.barrageItem, this.onDelete});

  @override
  Widget build(BuildContext context) {
    RxBool visible = true.obs;
    return Obx(() {
      return Visibility(
        visible: visible.value,
        child: Container(
          height: 20.h,
          margin: EdgeInsets.only(right: 10.w),
          padding: EdgeInsets.only(right: 10.w),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(10.r),
            color: Colors.black.withOpacity(0.5),
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Padding(
                padding: EdgeInsets.only(left: 2.w),
                child: ClipOval(
                  child: ImageUtils.getImage(
                      barrageItem.avatar ?? "", 16.w, 16.w,
                      fit: BoxFit.cover),
                ),
              ),
              Padding(
                padding: EdgeInsets.only(left: 5.w),
                child: Text(
                  "${barrageItem.nickname ?? ""}：${barrageItem.content ?? ""}",
                  style: TextStyles.common(12.sp, Colors.white),
                ),
              ),
            ],
          ),
        ),
      );
    });
  }
}
