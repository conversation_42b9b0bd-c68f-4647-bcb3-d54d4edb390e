import 'package:dada/common/values/colors.dart';
import 'package:dada/common/values/enums.dart';
import 'package:dada/common/values/text_styles.dart';
import 'package:dada/common/values/themes.dart';
import 'package:dada/components/widgets/custom_app_bar.dart';
import 'package:dada/components/widgets/input_text_page.dart';
import 'package:dada/components/widgets/label_item_widget.dart';
import 'package:dada/components/widgets/common_setting_list_item_widget.dart';
import 'package:dada/components/widgets/switch_button.dart';
import 'package:dada/generated/assets.dart';
import 'package:dada/generated/l10n.dart';
import 'package:dada/model/chat_group_label_entity.dart';
import 'package:dada/pages/chat/setting/group/group_all_member_list_page.dart';
import 'package:dada/pages/chat/setting/group/group_chat_setting_controller.dart';
import 'package:dada/pages/chat/setting/manager/group_manage_setting_page.dart';
import 'package:dada/pages/match/assemble/team/invite/invite_selected_user_page.dart';
import 'package:dada/pages/report/report_page.dart';
import 'package:dada/routers/router.dart';
import 'package:dada/utils/image_utils.dart';
import 'package:dada/utils/toast_utils.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:image_picker/image_picker.dart';
import 'package:tencent_cloud_chat_sdk/models/v2_tim_group_info.dart';
import 'package:tencent_cloud_chat_sdk/models/v2_tim_group_member_full_info.dart';

class GroupChatSettingsPage extends StatefulWidget {
  final V2TimGroupInfo groupInfo;

  const GroupChatSettingsPage({super.key, required this.groupInfo});

  @override
  State<GroupChatSettingsPage> createState() => _GroupChatSettingsPageState();
}

class _GroupChatSettingsPageState extends State<GroupChatSettingsPage> {
  final GroupChatSettingController controller = GroupChatSettingController();

  @override
  void initState() {
    super.initState();

    controller.v2timGroupInfo = widget.groupInfo;
    controller.groupID = widget.groupInfo.groupID;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.themeData.scaffoldBackgroundColor,
      appBar: CustomAppBar(
        title: "群详情",
      ),
      body: SingleChildScrollView(
        padding: EdgeInsets.only(bottom: ScreenUtil().bottomBarHeight + 10.h),
        child: GetBuilder(
          init: controller,
          builder: (controller) {
            return Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildGroupMemberListWidget(),
                _buildSeparatorLine(),
                _buildGroupInfoWidget(),
                _buildSeparatorLine(),
                _buildGroupMessageDisturbWidget(),
                _buildSeparatorLine(),
                _buildSettingGroupWidget(),
                _buildSeparatorLine(),
                _buildGroupLabelsWidget(),
                _buildSeparatorLine(),
                _buildExitGroupWidget(),
              ],
            );
          },
        ),
      ),
    );
  }

  Widget _buildGroupMemberListWidget() {
    if (!(controller.memberList?.isNotEmpty == true)) {
      return Container();
    }
    bool showMore = false;
    int showMaxLength = 19;
    List<V2TimGroupMemberFullInfo?> memberList = [];
    if (controller.memberList?.isNotEmpty == true) {
      if (controller.checkIsGroupOwnerOrManager()) {
        showMaxLength = 18;
        showMore = controller.memberList!.length > showMaxLength;
      } else {
        showMaxLength = 19;
        showMore = controller.memberList!.length > showMaxLength;
      }
    }

    if (showMore) {
      memberList.addAll(controller.memberList!.sublist(0, showMaxLength));
    } else {
      memberList.addAll(controller.memberList!);
    }

    memberList.add(V2TimGroupMemberFullInfo(userID: "+"));
    if (controller.checkIsGroupOwnerOrManager()) {
      memberList.add(V2TimGroupMemberFullInfo(userID: "-"));
    }

    return Padding(
      padding: EdgeInsets.only(bottom: 20.h, top: 15.h),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          _buildListView(memberList),
          Visibility(
            visible: showMore,
            child: GestureDetector(
              onTap: () {
                Get.to(
                  () => const GroupAllMemberListPage(),
                );
              },
              child: Padding(
                padding: EdgeInsets.only(top: 25.h),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text(
                      "查看更多群成员",
                      style: TextStyles.common(16.sp, AppColors.colorFF666666),
                    ),
                    SizedBox(
                      width: 5.w,
                    ),
                    ImageUtils.getImage(Assets.imagesCommonListMore, 8.w, 14.h),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildListView(List<V2TimGroupMemberFullInfo?> list) {
    return Padding(
      padding: EdgeInsets.only(left: 8.w),
      child: Wrap(
        spacing: 1.5.w,
        runSpacing: 15.h,
        children: list.map((memberInfo) {
          if (memberInfo != null) {
            return _buildGroupMemberListItem(memberInfo);
          }
          return Container();
        }).toList(),
      ),
    );
  }

  Widget _buildGroupMemberListItem(V2TimGroupMemberFullInfo memberInfo) {
    if (memberInfo.userID == "+") {
      return GestureDetector(
        onTap: () {
          Get.toNamed(GetRouter.createGroupChat,
                  parameters: {"isInviteMember": "1"},
                  arguments: controller.memberList)
              ?.then((value) {
            if (value != null) {
              if (value is List<String>) {
                controller.inviteMemberList(value);
              }
            }
          });
        },
        child: SizedBox(
          height: 80.h,
          width: 71.w,
          child: Column(
            children: [
              ImageUtils.getImage(
                  Assets.imagesGroupChatMemberListAdd, 55.w, 55.w),
            ],
          ),
        ),
      );
    }
    if (memberInfo.userID == "-") {
      return GestureDetector(
        onTap: () {
          Get.to(
            () => InviteSelectedUserPage(
              userList: controller.transferMemberListToFriendList(),
              isSingleSelected: false,
              isDeleteMember: true,
              callback: (userIds) {
                controller.kickOutOfMembers(userIds);
              },
            ),
          );
        },
        child: SizedBox(
          height: 80.h,
          width: 71.w,
          child: Column(
            children: [
              ImageUtils.getImage(
                  Assets.imagesGroupChatMemberListDelete, 55.w, 55.w),
            ],
          ),
        ),
      );
    }
    return GestureDetector(
      onTap: () {
        Get.toNamed(GetRouter.userProfile,
            parameters: {"userId": memberInfo.userID});
      },
      child: SizedBox(
        height: 81.h,
        width: 71.w,
        child: Column(
          children: [
            ClipOval(
              child: ImageUtils.getImage(memberInfo.faceUrl ?? "", 55.w, 55.w,
                  fit: BoxFit.cover),
            ),
            Padding(
              padding: EdgeInsets.only(top: 3.h),
              child: Text(
                memberInfo.nameCard?.isNotEmpty == true
                    ? memberInfo.nameCard!
                    : (memberInfo.nickName ?? ""),
                overflow: TextOverflow.ellipsis,
                style: TextStyles.normal(14.sp),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildGroupInfoWidget() {
    bool isOwner = controller.checkIsGroupOwner();
    bool isOwnerOrManager = controller.checkIsGroupOwnerOrManager();
    String? groupNotice = controller.groupInfo?.notice;
    if (groupNotice != null) {
      groupNotice = groupNotice.length > 10
          ? "${groupNotice.substring(0, 10)}..."
          : groupNotice;
    }
    return Column(
      children: [
        ///群名称
        CommonSettingListItemWidget(
          title: "群名称",
          height: 55.h,
          subTitle: controller.groupInfo?.groupName,
          hideMore: !isOwnerOrManager,
          onTap: () {
            if (isOwnerOrManager) {
              Get.to(
                () => InputTextPage(
                  title: "修改群名称",
                  text: controller.groupInfo?.groupName,
                  callback: (value) {
                    controller.updateGroupInfo(groupName: value);
                  },
                ),
              );
            }
          },
        ),

        ///在群里的昵称
        CommonSettingListItemWidget(
          title: "我在群的昵称",
          height: 55.h,
          subTitle: controller.inGroupShowName,
          onTap: () {
            Get.to(
              () => InputTextPage(
                title: "设置在群昵称",
                text: controller.inGroupShowName,
                callback: (value) {
                  controller.setInGroupShowName(value);
                },
              ),
            );
          },
        ),

        ///群头像
        isOwnerOrManager
            ? CommonSettingListItemWidget(
                title: "群头像",
                height: 75.h,
                onTap: () {
                  ToastUtils.showBottomSheet(
                    [S.current.album, S.current.camera],
                    onTap: (index) async {
                      XFile? imageFile = await ImagePicker().pickImage(
                          source: index == 0
                              ? ImageSource.gallery
                              : ImageSource.camera);
                      if (imageFile?.path.isNotEmpty == true) {
                        controller
                            .uploadImageAndUpdateGroupFaceUrl(imageFile!.path);
                      }
                    },
                  );
                },
                rightWidget: Row(
                  children: [
                    ClipOval(
                      child: ImageUtils.getImage(
                        controller.groupInfo?.faceUrl ?? "",
                        46.w,
                        46.w,
                        fit: BoxFit.cover,
                        placeholder: Assets.imagesGroupChatDefaultAvatar,
                        showPlaceholder: true,
                      ),
                    ),
                    Padding(
                      padding: EdgeInsets.only(left: 4.w),
                      child: ImageUtils.getImage(
                        Assets.imagesCommonListMore,
                        8.w,
                        14.h,
                      ),
                    ),
                  ],
                ),
              )
            : Container(),

        ///群公告
        CommonSettingListItemWidget(
          title: "群公告",
          height: 55.h,
          subTitle: groupNotice ?? "暂无",
          onTap: () {
            Get.to(
              () => InputTextPage(
                title: "群公告",
                text: controller.groupInfo?.notice,
                isMultiLine: true,
                readOnly: !isOwnerOrManager,
                maxLength: 300,
                callback: (value) {
                  controller.updateGroupInfo(notice: value);
                },
              ),
            );
          },
        ),

        ///群管理
        isOwner
            ? CommonSettingListItemWidget(
                title: "群管理",
                height: 55.h,
                subTitle: "",
                onTap: () {
                  Get.to(() => const GroupManageSettingPage());
                },
              )
            : Container(),
      ],
    );
  }

  Widget _buildGroupMessageDisturbWidget() {
    return CommonSettingListItemWidget(
      title: "消息免打扰",
      height: 55.h,
      rightWidget: SwitchButton(
        value: controller.v2timGroupInfo.recvOpt == 2,
        onChanged: (value) {
          controller.updateGroupMessageReceiveLimit(value);
        },
      ),
    );
  }

  Widget _buildGroupMessageHistoryListWidget() {
    return CommonSettingListItemWidget(
      title: "查找聊天记录",
      height: 55.h,
      subTitle: "",
      onTap: () {},
    );
  }

  Widget _buildSettingGroupWidget() {
    bool isOwner = controller.checkIsGroupOwner();
    bool isOwnerOrManager = controller.checkIsGroupOwnerOrManager();
    return Column(
      children: [
        CommonSettingListItemWidget(
          title: "清空聊天记录",
          height: 55.h,
          subTitle: "",
          onTap: () {
            controller.clearGroupHistoryList();
          },
        ),

        // CommonSettingListItemWidget(
        //   title: "设置当前聊天背景",
        //   height: 55.h,
        //   subTitle: controller.groupInfo?.inGroupNickname,
        //   onTap: () {},
        // ),

        ///在群里的昵称
        isOwner
            ? CommonSettingListItemWidget(
                title: "解散该群聊",
                height: 55.h,
                onTap: () {
                  controller.disbandGroup();
                },
                subTitle: "",
                hideMore: true,
              )
            : Container(),

        ///投诉
        !isOwnerOrManager
            ? CommonSettingListItemWidget(
                title: "投诉",
                height: 55.h,
                subTitle: "",
                onTap: () {
                  Get.to(() => ReportPage(
                      reportType: ReportType.group,
                      groupId: controller.groupID));
                },
              )
            : Container(),
      ],
    );
  }

  Widget _buildGroupLabelsWidget() {
    List<ChatGroupLabelEntity> labelList = [];
    if (controller.groupInfo?.labels?.isNotEmpty == true) {
      labelList.addAll(controller.groupInfo!.labels!);
    }
    if (controller.checkIsGroupOwnerOrManager()) {
      labelList.add(ChatGroupLabelEntity()..id = "+");
    }
    return Padding(
      padding: EdgeInsets.symmetric(vertical: 15.h),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: EdgeInsets.only(left: 15.w),
            child: Text(
              "群标签",
              style: TextStyles.normal(16.sp),
            ),
          ),
          Padding(
            padding: EdgeInsets.only(top: 10.h, left: 10.w, right: 5.w),
            child: Wrap(
              spacing: 5.w,
              runSpacing: 10.h,
              children:
                  labelList.map((e) => _buildGroupLabelItemWidget(e)).toList(),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildGroupLabelItemWidget(ChatGroupLabelEntity labelEntity) {
    if (labelEntity.id == "+") {
      return LabelItemWidget(
        text: "添加标签",
        isAddItem: true,
        borderColor: AppColors.colorFFE1E1E1,
        height: 30.h,
        borderRadius: 15.r,
        addAction: (value) {
          controller.addGroupLabel(value);
        },
      );
    }
    return LabelItemWidget(
      text: labelEntity.tagName ?? "",
      bgColor: AppColors.colorFFEAECF1,
      editable: controller.checkIsGroupOwner(),
      borderRadius: 15.r,
      borderColor: Colors.transparent,
      closeBtnColor: AppColors.colorFF999999,
      deleteAction: () {
        controller.deleteGroupLabel(labelEntity.id!);
      },
    );
  }

  Widget _buildExitGroupWidget() {
    return InkWell(
      onTap: () {
        controller.exitGroup();
      },
      child: Container(
        height: 40.h,
        alignment: Alignment.center,
        child: Text(
          "退出群聊",
          style: TextStyles.common(16.sp, AppColors.colorFFEA4A4A),
        ),
      ),
    );
  }

  Widget _buildSeparatorLine() {
    return Container(
      height: 10.h,
      color: AppColors.colorFFE5E5E5,
    );
  }
}
