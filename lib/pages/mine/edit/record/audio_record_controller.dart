import 'package:dada/utils/audio_recorder.dart';
import 'package:get/get.dart';


class AudioRecordController extends GetxController {
  int _currentIndex = 0;
  RxString currentParagraph = "".obs;
  List<String> paragraphs = [];

  RxInt currentRecordSecs = 0.obs;

  @override
  void onInit() {
    super.onInit();
    paragraphs = [
      "可以去找的东西\n往往是找不到的\n天下万物的来和去\n都有他的时间。",
    ];

    currentParagraph.value = paragraphs.first;
  }

  void changeParagraph() {
    if (_currentIndex >= paragraphs.length - 1) {
      _currentIndex = 0;
    } else {
      _currentIndex++;
    }
    currentParagraph.value = paragraphs[_currentIndex];
  }

  @override
  void onClose() {
    AudioRecorder().stopRecording();
    super.onClose();
  }
}
