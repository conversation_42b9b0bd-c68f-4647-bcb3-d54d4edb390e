import 'package:dada/common/values/colors.dart';
import 'package:dada/common/values/text_styles.dart';
import 'package:dada/common/values/themes.dart';
import 'package:dada/components/widgets/circle_progress_indicator.dart';
import 'package:dada/components/widgets/custom_app_bar.dart';
import 'package:dada/generated/assets.dart';
import 'package:dada/generated/l10n.dart';
import 'package:dada/pages/mine/edit/record/audio_record_controller.dart';
import 'package:dada/utils/audio_recorder.dart';
import 'package:dada/utils/image_utils.dart';
import 'package:dada/utils/time_utils.dart';
import 'package:dada/utils/toast_utils.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:permission_handler/permission_handler.dart';

class AudioRecordPage extends StatefulWidget {
  const AudioRecordPage({super.key});

  @override
  State<AudioRecordPage> createState() => _AudioRecordPageState();
}

class _AudioRecordPageState extends State<AudioRecordPage> {
  final controller = Get.put(AudioRecordController());
  final audioRecorder = AudioRecorder();

  String? title;

  @override
  void initState() {
    super.initState();

    title = Get.parameters['title'];
    audioRecorder.maxLength = int.parse(Get.parameters['maxLength'] ?? "30");
    audioRecorder.minLength = int.parse(Get.parameters['minLength'] ?? "5");

    WidgetsBinding.instance.addPostFrameCallback((_){
      Future.delayed(const Duration(milliseconds: 300), () {
        requestAudioRecorderPermission();
      });
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.themeData.scaffoldBackgroundColor,
      appBar: CustomAppBar(title: S.current.audioSignature),
      body: Container(
        color: AppColors.colorFFF5F5F5,
        child: Column(
          children: [
            _buildParagraphWidget(),
            const Spacer(),
            _buildRecordWidget(),
          ],
        ),
      ),
    );
  }

  Widget _buildParagraphWidget() {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 15.w, vertical: 10.h),
      height: 230.h,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(10.r),
        color: AppTheme.themeData.colorScheme.surface,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: EdgeInsets.only(top: 15.h, left: 15.w, right: 15.w),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  S.current.readParagraph,
                  style: TextStyles.normal(16.sp,
                      c: AppTheme.themeData.textTheme.bodyMedium?.color),
                ),
                GestureDetector(
                  onTap: () {
                    controller.changeParagraph();
                  },
                  child: Row(
                    children: [
                      ImageUtils.getImage(
                          Assets.imagesRecordParagraphExchange, 15.w, 15.w),
                      SizedBox(
                        width: 5.w,
                      ),
                      Text(
                        S.current.changeParagraph,
                        style: TextStyles.normal(16.sp,
                            c: AppTheme.themeData.textTheme.labelMedium?.color),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
          SizedBox(
            height: 14.h,
          ),
          Container(
            height: 1.h,
            color: AppTheme.themeData.colorScheme.inverseSurface,
          ),
          Padding(
            padding: EdgeInsets.only(top: 14.h, left: 18.w, right: 18.w),
            child: Obx(() {
              return Text(
                controller.currentParagraph.value,
                style: TextStyle(
                    color: AppTheme.themeData.textTheme.labelMedium?.color,
                    fontSize: 18.sp,
                    height: 1.8),
              );
            }),
          )
        ],
      ),
    );
  }

  Widget _buildRecordWidget() {
    return GetBuilder<AudioRecorder>(
      init: audioRecorder,
      global: false,
      builder: (recorder) {
        return Column(
          children: [
            Text(
              TimeUtils.countdownTime(recorder.duration ?? 0),
              style: TextStyles.common(
                25.sp,
                AppTheme.themeData.textTheme.labelMedium?.color,
                w: FontWeight.w900,
              ),
            ),
            SizedBox(
              height: 5.h,
            ),
            Text(
              (recorder.status == AudioRecorderStatus.recording ||
                      recorder.status == AudioRecorderStatus.pause)
                  ? S.current.recording
                  : S.current.startRecord,
              style: TextStyles.common(
                16.sp,
                AppTheme.themeData.textTheme.bodyMedium?.color,
              ),
            ),
            SizedBox(
              height: 20.h,
            ),
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                ///重录按钮
                Visibility(
                  visible: recorder.status == AudioRecorderStatus.pause ||
                      recorder.status == AudioRecorderStatus.stop,
                  child: GestureDetector(
                    onTap: () {
                      recorder.reset();
                      recorder.startRecording();
                    },
                    child: Column(
                      children: [
                        SizedBox(
                          height: 25.h,
                        ),
                        ImageUtils.getImage(
                            Assets.imagesRecordRepeatBtn, 40.w, 40.w),
                        SizedBox(
                          height: 5.h,
                        ),
                        Text(
                          S.current.recordAgain,
                          style: TextStyles.common(14.sp,
                              AppTheme.themeData.textTheme.bodyMedium?.color),
                        ),
                      ],
                    ),
                  ),
                ),

                ///开始录制按钮
                Padding(
                  padding: EdgeInsets.symmetric(horizontal: 20.w),
                  child: Stack(
                    children: [
                      Visibility(
                        visible: recorder.status == AudioRecorderStatus.idle ||
                            recorder.status == AudioRecorderStatus.stop,
                        child: GestureDetector(
                          onTap: () {
                            if (recorder.status ==
                                AudioRecorderStatus.recording) {
                              return;
                            } else if (recorder.status ==
                                AudioRecorderStatus.pause) {
                              recorder.resumeRecording();
                              return;
                            }
                            recorder.startRecording();
                          },
                          child: ImageUtils.getImage(
                              Assets.imagesRecordStartBtn, 80.w, 80.w),
                        ),
                      ),
                      Visibility(
                        visible:
                            recorder.status == AudioRecorderStatus.recording,
                        child: GestureDetector(
                          onTap: () {
                            recorder.stopRecording();
                            if ((recorder.duration ?? 0) < recorder.minLength) {
                              recorder.reset();
                              ToastUtils.showToast("录音时间太短，请重新录制");
                            } else {
                              recorder.stopRecording();
                            }
                          },
                          child: Stack(
                            alignment: AlignmentDirectional.center,
                            children: [
                              CustomPaint(
                                size: Size(80.w, 80.w),
                                painter: CircleProgressIndicator(
                                  80.w,
                                  (recorder.duration ?? 0) / recorder.maxLength,
                                  color: const Color(0xFFF63232),
                                  strokeWidth: 1.w,
                                ),
                              ),
                              Container(
                                width: 24.w,
                                height: 24.w,
                                decoration: BoxDecoration(
                                  color: const Color(0xFFF63232),
                                  borderRadius: BorderRadius.circular(5.r),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ],
                  ),
                ),

                ///保存按钮
                Visibility(
                  visible: recorder.status == AudioRecorderStatus.pause ||
                      recorder.status == AudioRecorderStatus.stop,
                  child: GestureDetector(
                    onTap: () async {
                      if ((recorder.duration ?? 0) < recorder.minLength) {
                        recorder.reset();
                        ToastUtils.showToast("录音时长过短");
                        return;
                      }
                      if (recorder.filePath != null && recorder.duration != null) {
                        var result = {
                          "filePath": recorder.filePath,
                          "duration": recorder.duration,
                        };
                        Get.back(result: result);
                      }
                    },
                    child: Column(
                      children: [
                        SizedBox(
                          height: 25.h,
                        ),
                        ImageUtils.getImage(
                            Assets.imagesRecordSaveBtn, 40.w, 40.w),
                        SizedBox(
                          height: 5.h,
                        ),
                        Text(
                          S.current.save,
                          style: TextStyles.common(14.sp,
                              AppTheme.themeData.textTheme.bodyMedium?.color),
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
            SizedBox(
              height: 50.h,
            ),
          ],
        );
      },
    );
  }

  void requestAudioRecorderPermission() async {
    var status = await Permission.microphone.status;
    if (status.isDenied) {
      // 请求权限
      status = await Permission.microphone.request();
    }

    if (status.isGranted) {
      // 权限已授予
      print("录音权限已授予");
    } else if (status.isPermanentlyDenied) {
      // 用户永久拒绝权限
      ToastUtils.showDialog(
        content: "录音权限未开启，要授予权限才能使用录音功能",
        confirmBtnTitle: "去开启",
        onConfirm: () {
          openAppSettings();
        },
      );
    } else {
      // 其他情况
      ToastUtils.showToast("录音权限请求被拒绝");
    }
  }

  @override
  void dispose() {
    audioRecorder.stopRecording();
    super.dispose();
  }
}
