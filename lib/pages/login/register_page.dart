import 'package:dada/common/values/colors.dart';
import 'package:dada/common/values/fonts_family.dart';
import 'package:dada/common/values/text_styles.dart';
import 'package:dada/common/values/themes.dart';
import 'package:dada/components/widgets/custom_app_bar.dart';
import 'package:dada/components/widgets/custom_text_field.dart';
import 'package:dada/components/widgets/permission_use_desc_dialog.dart';
import 'package:dada/generated/assets.dart';
import 'package:dada/generated/l10n.dart';
import 'package:dada/pages/login/login_controller.dart';
import 'package:dada/utils/image_utils.dart';
import 'package:dada/utils/log.dart';
import 'package:dada/utils/time_utils.dart';
import 'package:dada/utils/toast_utils.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

import 'package:dada/routers/router.dart';
import 'package:image_picker/image_picker.dart';
import 'package:numberpicker/numberpicker.dart';
import 'package:permission_handler/permission_handler.dart';

class RegisterPage extends StatefulWidget {
  const RegisterPage({super.key});

  @override
  State<RegisterPage> createState() => _RegisterPageState();
}

class _RegisterPageState extends State<RegisterPage> {
  final controller = Get.find<LoginController>();
  final TextEditingController _textEditingController = TextEditingController();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      resizeToAvoidBottomInset: false,
      backgroundColor: AppTheme.themeData.scaffoldBackgroundColor,
      appBar: CustomAppBar(),
      body: ListView(
        children: [
          ///title
          Padding(
            padding: EdgeInsets.only(left: 30.w),
            child: Text(
              S.current.registerTitle,
              style: TextStyle(
                fontFamily: FontsFamily.youSheBiaoTiHei,
                fontSize: 20.sp,
                fontWeight: FontWeight.w400,
                height: 1.1,
                color: AppTheme.themeData.textTheme.labelMedium?.color,
              ),
            ),
          ),

          SizedBox(height: 10.h),

          _buildTextTipLabel(),

          SizedBox(height: 10.h),

          ///avatar
          _avatarWidget(),

          SizedBox(height: 32.h),

          ///昵称
          _nicknameWidget(),

          SizedBox(height: 22.h),

          ///生日
          //_birthdayWidget(),

          ///年龄
          _ageWidget(),

          SizedBox(height: 22.h),

          ///星座
          _constellationWidget(),

          SizedBox(height: 22.h),

          ///性别
          _sexWidget(),

          SizedBox(height: 32.h),

          ///个人社交习惯
          _socialHabits(),

          SizedBox(height: 25.h),

          ///底部按钮
          _bottomBtn(),
        ],
      ),
    );
  }

  Widget _avatarWidget() {
    return GestureDetector(
      onTap: () async {
        ToastUtils.showBottomSheet(
          [S.current.album, S.current.camera, '系统头像(部分)'],
          onTap: (index) async {
            if (index == 0 || index == 1) {
              if (index == 0) {
                PermissionStatus status = await Permission.photos.status;
                if (status.isGranted || status.isDenied) {
                  if (GetPlatform.isAndroid) {
                    ToastUtils.showTopDialog(
                      barrierDismissible: false,
                      child: const PermissionUseDescDialog(
                          title: "相册权限说明", desc: "获取图片用于设置头像"),
                    );
                  }
                  status = await Permission.photos.request();
                  if (Get.isDialogOpen == true) {
                    Get.back();
                  }
                  if (status != PermissionStatus.granted) {
                    return;
                  }
                }
              } else if (index == 1) {
                PermissionStatus status = await Permission.photos.status;
                if (status.isGranted || status.isDenied) {
                  if (GetPlatform.isAndroid) {
                    ToastUtils.showTopDialog(
                      barrierDismissible: false,
                      child: const PermissionUseDescDialog(
                          title: "相机权限说明", desc: "拍摄图片用于设置头像, 拍摄后的图片将存放在系统照片中"),
                    );
                  }
                  status = await Permission.camera.request();
                  if (Get.isDialogOpen == true) {
                    Get.back();
                  }
                  if (status != PermissionStatus.granted) {
                    return;
                  }
                }
              }
              XFile? imageFile = await ImagePicker().pickImage(
                  source:
                      index == 0 ? ImageSource.gallery : ImageSource.camera);
              controller.selectedImagePath.value = imageFile?.path ?? "";
            } else {
              Get.toNamed(GetRouter.selectAvatar, parameters: {"price": "0"})
                  ?.then((value) {
                if (value != null) {
                  controller.selectedImagePath.value = value;
                }
              });
            }
          },
        );
        // ToastUtils.showBottomSheet(
        //   [S.current.album, S.current.camera],
        //   onTap: (index) async {
        //     XFile? imageFile = await ImagePicker().pickImage(
        //         source: index == 0 ? ImageSource.gallery : ImageSource.camera);
        //     controller.selectedImagePath.value = imageFile?.path ?? "";
        //   },
        // );
        // Get.toNamed(GetRouter.selectAvatar, parameters: {"price": "0"})
        //     ?.then((value) {
        //   if (value != null) {
        //     controller.selectedImagePath.value = value;
        //   }
        // });
      },
      child: Row(
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          Padding(
            padding: EdgeInsets.only(left: 30.w),
            child: Obx(() {
              return ClipOval(
                child: ImageUtils.getImage(
                  controller.selectedImagePath.value,
                  70.w,
                  70.w,
                  showPlaceholder: true,
                  fit: BoxFit.cover,
                ),
              );
            }),
          ),
        ],
      ),
    );
  }

  Widget _nicknameWidget() {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 37.5.w),
      child: Column(
        children: [
          Row(
            children: [
              ImageUtils.getImage(Assets.imagesRegisterTagIcon, 23.w, 21.w),
              SizedBox(width: 4.w),
              Text(
                S.current.nickname,
                style: TextStyles.bold(16.sp),
              ),
            ],
          ),
          SizedBox(height: 12.h),
          Container(
            height: 45.h,
            padding: EdgeInsets.only(left: 21.w),
            decoration: BoxDecoration(
              color: AppTheme.themeData.inputDecorationTheme.fillColor,
              borderRadius: BorderRadius.circular(45.h / 2),
            ),
            child: CustomTextField.build(
              controller: _textEditingController,
              maxLength: 8,
              hintText: S.current.nicknamePlaceholder,
              textInputAction: TextInputAction.done,
              keyboardType: TextInputType.text,
              style: TextStyles.normal(16.sp),
              onChanged: (value) {
                controller.inputNickname.value = _textEditingController.text;
              },
              /* suffixIcon: GestureDetector(
                onTap: () async {
                  String? nickname = await controller.getRandomNickname();
                  controller.inputNickname.value = nickname ?? "";
                  _textEditingController.text = nickname ?? "";
                },
                child: Container(
                  padding: EdgeInsets.only(right: 15.w),
                  alignment: Alignment.centerRight,
                  child: Row(
                    children: [
                      Icon(
                        Icons.refresh,
                        color: AppTheme.themeData.textTheme.titleMedium?.color,
                        size: 20.w,
                      ),
                      SizedBox(width: 2.w),
                      Text(
                        S.current.random,
                        style: AppTheme.themeData.textTheme.titleMedium,
                      ),
                    ],
                  ),
                ),
              ),
              suffixIconConstraints: BoxConstraints(
                maxWidth: 55.w + 15.w,
                maxHeight: 45.h,
                minWidth: 16.w,
                minHeight: 45.h,
              ), */
            ),
          ),
        ],
      ),
    );
  }

  Widget _ageWidget() {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 37.5.w),
      child: Column(
        children: [
          Row(
            children: [
              ImageUtils.getImage(Assets.imagesRegisterTagIcon, 23.w, 21.w),
              SizedBox(width: 4.w),
              Text(
                "年龄",
                style: TextStyles.bold(16.sp),
              ),
            ],
          ),
          SizedBox(height: 12.h),
          Obx(() {
            return Container(
              height: 45.h,
              padding: EdgeInsets.only(left: 6.w),
              decoration: BoxDecoration(
                color: AppTheme.themeData.inputDecorationTheme.fillColor,
                borderRadius: BorderRadius.circular(45.h / 2),
              ),
              child: NumberPicker(
                value: controller.inputAge.value,
                minValue: 14,
                maxValue: 100,
                step: 1,
                onChanged: (value) {
                  controller.inputAge.value = value;
                  //controller.hasChanges.value = true;
                },
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(8.r),
                  border:
                      Border.all(color: AppTheme.themeData.colorScheme.outline),
                ),
                textStyle: TextStyles.normal(16.sp), // 调整文本大小以适应选择框
                selectedTextStyle: TextStyles.bold(16.sp, c: Colors.black),
                axis: Axis.horizontal, // 改为垂直方向
                haptics: true, // 可选：启用触觉反馈
                itemHeight: 50.h, // 设置每个选项的高度
                itemWidth: 99.w, // 设置每个选项的宽度
                itemCount: 3, // 设置选项数量
              ),
            );
          }),
        ],
      ),
    );
  }

  Widget _constellationWidget() {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 37.5.w),
      child: Column(
        children: [
          Row(
            children: [
              ImageUtils.getImage(Assets.imagesRegisterTagIcon, 23.w, 21.w),
              SizedBox(width: 4.w),
              Text(
                "星座",
                style: TextStyles.bold(16.sp),
              ),
            ],
          ),
          SizedBox(height: 12.h),
          Obx(() {
            return GestureDetector(
              onTap: () {
                Get.bottomSheet(
                  Container(
                    height: 300.h,
                    decoration: BoxDecoration(
                      color: AppTheme.themeData.scaffoldBackgroundColor,
                      borderRadius: BorderRadius.only(
                        topLeft: Radius.circular(20.r),
                        topRight: Radius.circular(20.r),
                      ),
                    ),
                    child: Column(
                      children: [
                        SizedBox(height: 10.h),
                        Text(
                          "选择星座",
                          style: TextStyles.bold(18.sp),
                        ),
                        SizedBox(height: 10.h),
                        Expanded(
                          child: ListView.builder(
                            itemCount: controller.constellation.length,
                            itemBuilder: (context, index) {
                              return ListTile(
                                title: Text(
                                  controller.constellation[index],
                                  style: TextStyles.normal(16.sp),
                                ),
                                onTap: () {
                                  controller.selectedConstellation.value =
                                      controller.constellation[index];
                                  Get.back();
                                },
                              );
                            },
                          ),
                        ),
                      ],
                    ),
                  ),
                );
              },
              child: Container(
                height: 45.h,
                padding: EdgeInsets.only(left: 21.w, right: 15.w),
                decoration: BoxDecoration(
                  color: AppTheme.themeData.inputDecorationTheme.fillColor,
                  borderRadius: BorderRadius.circular(45.h / 2),
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      controller.selectedConstellation.value.isNotEmpty
                          ? controller.selectedConstellation.value
                          : "请选择星座",
                      style: TextStyles.common(16.sp,
                          AppTheme.themeData.textTheme.labelMedium?.color),
                    ),
                    GestureDetector(
                      onTap: () {
                        Get.bottomSheet(
                          Container(
                            height: 300.h,
                            decoration: BoxDecoration(
                              color: AppTheme.themeData.scaffoldBackgroundColor,
                              borderRadius: BorderRadius.only(
                                topLeft: Radius.circular(20.r),
                                topRight: Radius.circular(20.r),
                              ),
                            ),
                            child: Column(
                              children: [
                                SizedBox(height: 10.h),
                                Text(
                                  "选择星座",
                                  style: TextStyles.bold(18.sp),
                                ),
                                SizedBox(height: 10.h),
                                Expanded(
                                  child: ListView.builder(
                                    itemCount: controller.constellation.length,
                                    itemBuilder: (context, index) {
                                      return ListTile(
                                        title: Text(
                                          controller.constellation[index],
                                          style: TextStyles.normal(16.sp),
                                        ),
                                        onTap: () {
                                          controller
                                                  .selectedConstellation.value =
                                              controller.constellation[index];
                                          Get.back();
                                        },
                                      );
                                    },
                                  ),
                                ),
                              ],
                            ),
                          ),
                        );
                      },
                      child: ImageUtils.getImage(
                          Assets.imagesNavigatorNext, 7.w, 13.h),
                    ),
                  ],
                ),
              ),
            );
          }),
        ],
      ),
    );
  }

  Widget _birthdayWidget() {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 37.5.w),
      child: Column(
        children: [
          Row(
            children: [
              ImageUtils.getImage(Assets.imagesRegisterTagIcon, 23.w, 21.w),
              SizedBox(width: 4.w),
              Text(
                S.current.birthday,
                style: TextStyles.bold(16.sp),
              ),
            ],
          ),
          SizedBox(height: 12.h),
          GestureDetector(
            onTap: () async {
              DateTime initialDate =
                  controller.selectedBirthday.value.year != 1970
                      ? controller.selectedBirthday.value
                      : DateTime.now();
              DateTime? result = await showDatePicker(
                  context: context,
                  initialDate: initialDate,
                  firstDate: DateTime(1950),
                  lastDate: DateTime.now(),
                  cancelText: S.current.cancel,
                  confirmText: S.current.sure,
                  locale: Get.deviceLocale);
              if (result != null) {
                controller.selectedBirthday.value = result;
              }
              Log.v(result);
            },
            child: Container(
              height: 45.h,
              padding: EdgeInsets.only(left: 21.w, right: 15.w),
              decoration: BoxDecoration(
                color: AppTheme.themeData.inputDecorationTheme.fillColor,
                borderRadius: BorderRadius.circular(45.h / 2),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Obx(() {
                    if (controller.selectedBirthday.value.year != 1970) {
                      return Text(
                        TimeUtils.dateFormatString(
                          dateTime: controller.selectedBirthday.value,
                          formatList: ["yyyy", "-", "mm", "-", "dd"],
                        ),
                        style: TextStyles.common(16.sp,
                            AppTheme.themeData.textTheme.labelMedium?.color),
                      );
                    }
                    return Text(
                      S.current.birthdayPlaceholder,
                      style: TextStyles.common(
                          16.sp, AppTheme.themeData.hintColor),
                    );
                  }),
                  ImageUtils.getImage(Assets.imagesNavigatorNext, 7.w, 13.h),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _sexWidget() {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 37.5.w),
      child: Column(
        children: [
          Row(
            children: [
              ImageUtils.getImage(Assets.imagesRegisterTagIcon, 23.w, 21.w),
              SizedBox(width: 5.w),
              Text(
                S.current.sex,
                style: TextStyles.bold(16.sp),
              ),
            ],
          ),
          SizedBox(height: 12.h),
          Container(
            padding: EdgeInsets.only(left: 4.w),
            child: Row(
              children: [
                Obx(
                  () => _sexItemWidget(
                    Assets.imagesSexMale,
                    Assets.imagesSexMaleSelected,
                    S.current.boy,
                    controller.selectedSexIndex.value == 0,
                    () {
                      controller.selectedSexIndex.value = 0;
                    },
                  ),
                ),
                SizedBox(width: 10.w),
                Obx(
                  () => _sexItemWidget(
                    Assets.imagesSexFemale,
                    Assets.imagesSexFemaleSelected,
                    S.current.girl,
                    controller.selectedSexIndex.value == 1,
                    () {
                      controller.selectedSexIndex.value = 1;
                    },
                  ),
                ),
              ],
            ),
          )
        ],
      ),
    );
  }

  Widget _sexItemWidget(String icon, String selectedIcon, String text,
      bool selected, Function() onTap) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        width: 100.w,
        height: 45.h,
        alignment: Alignment.center,
        decoration: BoxDecoration(
          color: selected
              ? AppTheme.themeData.colorScheme.surface
              : AppTheme.themeData.colorScheme.tertiary,
          borderRadius: BorderRadius.circular(5.r),
          border: selected
              ? Border.all(
                  color: AppTheme.themeData.colorScheme.onTertiaryContainer,
                  width: 1.w)
              : null,
        ),
        child: Stack(
          children: [
            Positioned(
              right: 0,
              top: 0,
              child: Visibility(
                visible: selected,
                child: ImageUtils.getImage(
                    Assets.imagesSexItemSelected, 20.w, 16.h),
              ),
            ),
            Positioned.fill(
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  ImageUtils.getImage(
                      selected ? selectedIcon : icon, 17.w, 17.w),
                  SizedBox(
                    width: 4.w,
                  ),
                  Text(
                    text,
                    style: TextStyle(
                        fontSize: 16.sp,
                        color: selected
                            ? AppTheme.themeData.colorScheme.onTertiary
                            : AppTheme.themeData.textTheme.labelSmall?.color),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _socialHabits() {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 41.w),
      child: Column(
        children: [
          Row(
            children: [
              ImageUtils.getImage(Assets.imagesRegisterTagIcon, 23.w, 21.w),
              SizedBox(width: 5.w),
              Text(
                S.current.socialHabits,
                style: TextStyles.bold(18.sp, f: FontsFamily.youSheBiaoTiHei),
              ),
            ],
          ),
          SizedBox(height: 17.h),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children:
                controller.habits.map((e) => _habitItemWidget(e)).toList(),
          ),
        ],
      ),
    );
  }

  Widget _habitItemWidget(String title) {
    return Obx(
      () {
        double width = (ScreenUtil().screenWidth - 41.w * 2) / 5;
        int index = controller.habits.indexOf(title);
        bool selected = controller.selectedHabitIndex.value == index;
        Color color = controller.habitsColors[index];
        return GestureDetector(
          onTap: () {
            controller.selectedHabitIndex.value = index;
          },
          child: Column(
            children: [
              Container(
                height: 39.h,
                width: width,
                padding: EdgeInsets.only(right: 1.w),
                child: Stack(
                  children: [
                    Column(
                      children: [
                        const Spacer(),
                        Container(
                          height: 10.h,
                          decoration: BoxDecoration(
                            color: color,
                            borderRadius: BorderRadius.only(
                              topLeft: index == 0
                                  ? Radius.circular(2.r)
                                  : Radius.zero,
                              bottomLeft: index == 0
                                  ? Radius.circular(2.r)
                                  : Radius.zero,
                              topRight: index == controller.habits.length - 1
                                  ? Radius.circular(2.r)
                                  : Radius.zero,
                              bottomRight: index == controller.habits.length - 1
                                  ? Radius.circular(2.r)
                                  : Radius.zero,
                            ),
                          ),
                        ),
                      ],
                    ),
                    Container(
                      alignment: Alignment.topRight,
                      padding: EdgeInsets.only(right: 3.w),
                      child: Visibility(
                        visible: selected,
                        child: ImageUtils.getImage(
                            Assets.imagesRegisterHabitSelected, 32.w, 37.h),
                      ),
                    ),
                  ],
                ),
              ),
              SizedBox(height: 3.h),
              Text(
                title,
                style: TextStyles.normal(14.sp),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _bottomBtn() {
    return GestureDetector(
      onTap: () {
        controller.registerNext();
      },
      child: Container(
        alignment: Alignment.center,
        margin: EdgeInsets.only(bottom: 20.h),
        padding: EdgeInsets.symmetric(horizontal: 35.w),
        height: 47.h,
        decoration: const BoxDecoration(
          image: DecorationImage(
            image: AssetImage(Assets.imagesLoginBtnBg),
          ),
        ),
        child: Center(
          child: Text(
            "下一步",
            style: TextStyle(
              color: AppTheme.themeData.colorScheme.onSecondaryContainer,
              fontSize: 16.sp,
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildTextTipLabel() {
    return Padding(
      padding: EdgeInsets.only(left: 30.w),
      child: Text(
        "搭吖极度重视您的隐私安全，绝对不会泄露您的任何信息",
        style: TextStyles.common(12.sp, AppColors.colorFF999999),
      ),
    );
  }

  @override
  void dispose() {
    super.dispose();
  }
}
