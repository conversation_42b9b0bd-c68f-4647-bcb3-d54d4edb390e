import 'dart:convert';

import 'package:dada/model/chat_im_custom_msg_entity.dart';
import 'package:dada/model/small_room_mail_unread_msg_entity.dart';
import 'package:dada/model/system_config_entity.dart';
import 'package:dada/model/teen_mode_status_entity.dart';
import 'package:dada/pages/chat/conversation/chat_conversation_list_controller.dart';
import 'package:dada/pages/main/dialog/new_account_welfare_dialog.dart';
import 'package:dada/pages/mine/mine_controller.dart';
import 'package:dada/pages/settings/teen_mode/teen_mode_setting_page.dart';
import 'package:dada/pages/small_room/small_room_controller.dart';
import 'package:dada/services/event_bus/event_bus_engine.dart';
import 'package:dada/services/im/chat_im_callback.dart';
import 'package:dada/services/im/chat_im_custom_msg_type.dart';
import 'package:dada/services/im/chat_im_manager.dart';
import 'package:dada/services/network/api_service.dart';
import 'package:dada/services/user/user_service.dart';
import 'package:dada/utils/toast_utils.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:tencent_cloud_chat_sdk/enum/message_elem_type.dart';
import 'package:tencent_cloud_chat_sdk/models/v2_tim_message.dart';

class MainController extends GetxController {
  RxInt currentIndex = 0.obs;
  RxInt previousIndex = 0.obs;

  ///聊天 tab 角标
  Rx<String?> chatTabBadgeCount = Rx<String?>(null);
  RxInt newFriendApplyUnreadCount = 0.obs;
  RxInt unreadMessageCount = 0.obs;

  ///搭圈 tab 角标
  Rx<String?> dynamicTabBadgeCount = Rx<String?>(null);
  RxInt postResonateUnreadCount = 0.obs;
  RxInt postMsgUnreadCount = 0.obs;

  ///搭搭 tab 角标
  Rx<String?> dadaTabBadgeCount = Rx<String?>(null);
  RxInt mailSystemUnreadCount = 0.obs;
  RxInt mailDadaUnreadCount = 0.obs;
  RxInt mailMsgUnreadCount = 0.obs;
  RxInt mailUnreadCount = 0.obs;

  ///青少年模式
  TeenModeStatusEntity? teenModeStatusEntity;

  @override
  void onReady() {
    super.onReady();

    addNewMsgListener();
    ChatIMManager.sharedInstance.clearC2CMessageUnReadCount("1");

    Future.delayed(const Duration(milliseconds: 300), () {
      checkTeenMode();
      loadUserInfo();
      loadSystemConfig();
    });

    showNewAccountDialog();
  }

  void switchTab(int i) async {
    // if (kDebugMode) {
    //   ToastUtils.showToast("switchTab: $i");
    // }
    if (i == 0 || i == 1) {
      bool isOpen = await checkCurrentTeenModeStatus();
      if (isOpen) {
        return;
      }
    }
    previousIndex.value = currentIndex.value;
    currentIndex.value = i;
    update();

    if (i == 2) {
      if (Get.isRegistered<SmallRoomController>(tag: "mine_small_room_tag")) {
        Get.find<SmallRoomController>(tag: "mine_small_room_tag")
            .checkShouldReload();
      }
    } else if (i == 3) {
      if (Get.isRegistered<ChatConversationListController>()) {
        Get.find<ChatConversationListController>().loadData(nexSeq: 0);
      }
    } else if (i == 4) {
      ///每次切换到我的tab，就刷新接口.
      if (Get.isRegistered<MineController>()) {
        Get.find<MineController>().loadUserInfo();
      }
    }
  }

  void updateChatTabBadgeCount(int count) {
    if (count != 0) {
      chatTabBadgeCount.value = "$count";
    } else {
      chatTabBadgeCount.value = null;
    }
  }

  void updateUnreadMessageCount(int count) {
    unreadMessageCount.value = count;
    updateChatTabBadgeCount(
        unreadMessageCount.value + newFriendApplyUnreadCount.value);
  }

  void getChatTabUnreadCount() async {
    int unreadCount =
        await ChatIMManager.sharedInstance.getFriendApplicationUnreadCount();
    newFriendApplyUnreadCount.value = unreadCount;
    updateChatTabBadgeCount(
        unreadMessageCount.value + newFriendApplyUnreadCount.value);
  }

  void reduceUnreadMessageCount(int reduceCount) async {
    if (chatTabBadgeCount.value != null) {
      int? count =
          await ChatIMManager.sharedInstance.getTotalUnreadMessageCount();
      if (count != null) {
        count -= reduceCount;
        updateChatTabBadgeCount(count);
      }
    }
  }

  ///获取搭圈未读消息数
  void getDynamicTabUnreadCount() async {
    postResonateUnreadCount.value =
        await ApiService().getPostResonateUnreadCount();
    postMsgUnreadCount.value = await ApiService().getDynamicUnreadCount() ?? 0;
    resetDynamicTabBadgeCount();
  }

  ///刷新共鸣未读数
  void refreshPostResonateUnreadCount() async {
    postResonateUnreadCount.value =
        await ApiService().getPostResonateUnreadCount();
    resetDynamicTabBadgeCount();
  }

  ///刷新搭圈消息未读数
  void refreshPostMsgUnreadCount() async {
    postMsgUnreadCount.value = await ApiService().getDynamicUnreadCount() ?? 0;
    resetDynamicTabBadgeCount();
  }

  ///刷新搭圈tab角标
  void resetDynamicTabBadgeCount() {
    dynamicTabBadgeCount.value =
        (postResonateUnreadCount.value + postMsgUnreadCount.value).toString();
    if (dynamicTabBadgeCount.value == "0") {
      dynamicTabBadgeCount.value = null;
    }
  }

  ///获取搭搭小屋tab未读消息数
  void getDadaTabUnreadCount() async {
    SmallRoomMailUnreadMsgEntity? mailUnreadMsgEntity =
        await ApiService().getSmallRoomMailUnreadCount();
    mailSystemUnreadCount.value = mailUnreadMsgEntity?.system ?? 0;
    mailDadaUnreadCount.value = mailUnreadMsgEntity?.daNo ?? 0;
    mailMsgUnreadCount.value = mailUnreadMsgEntity?.barrage ?? 0;
    mailUnreadCount.value = mailSystemUnreadCount.value +
        mailDadaUnreadCount.value +
        mailMsgUnreadCount.value;
    dadaTabBadgeCount.value = mailUnreadCount.value.toString();
    if (dadaTabBadgeCount.value == "0") {
      dadaTabBadgeCount.value = null;
    }
  }

  void loadUserInfo() async {
    ApiService().getUserInfo(UserService().user!.id!, showLoading: false);
  }

  void loadSystemConfig() async {
    SystemConfigEntity? map = await ApiService().getSystemConfig();
    UserService().systemConfig = map;
  }

  void showNewAccountDialog() {
    String? isCreateNewAccount = Get.parameters["isCreateNewAccount"];
    if (isCreateNewAccount == "1") {
      Future.delayed(
        const Duration(
          milliseconds: 800,
        ),
        () {
          ToastUtils.showDialog(
            dialog: const NewAccountWelfareDialog(),
          );

          sendNewUserWelcomeMsg();
        },
      );
    }
  }

  ///给新用户发送消息
  void sendNewUserWelcomeMsg() async {
    ApiService().sendWelcomeMsgToNewUser();
  }

  void addNewMsgListener() {
    ChatIMManager.sharedInstance.addListener(ChatImListener(
      onReceiveNewMessage: (V2TimMessage msg) {
        if (msg.elemType == MessageElemType.V2TIM_ELEM_TYPE_CUSTOM &&
            msg.customElem?.data != null) {
          ChatImCustomMsgEntity customMsgItem = ChatImCustomMsgEntity.fromJson(
            jsonDecode(msg.customElem!.data!),
          );
          if (customMsgItem.type ==
              ChatImCustomMsgType.DaCircleMessageNoticeMsg) {
            postMsgUnreadCount.value++;
            resetDynamicTabBadgeCount();
          } else if (customMsgItem.type ==
              ChatImCustomMsgType.DynamicPostResonateNoticeMsg) {
            postResonateUnreadCount.value++;
            resetDynamicTabBadgeCount();
          } else if (customMsgItem.type ==
              ChatImCustomMsgType.SmallRoomEmailNoticeMsg) {
            mailUnreadCount.value++;
            dadaTabBadgeCount.value = mailUnreadCount.value.toString();
            EventBusEngine.fire(event: BusEvent.smallRoomMailHasNewMsg);
          }
        }
      },
    ));
  }

  void checkTeenMode() async {
    DateTime now = DateTime.now();
    if (now.hour <= 6 || now.hour >= 22) {
      TeenModeStatusEntity? entity =
          await ApiService().getTeenModeStatus(showLoading: false);
      teenModeStatusEntity = entity;
      if (entity?.toggle == "1") {
        Get.dialog(
          barrierDismissible: false,
          useSafeArea: false,
          barrierColor: Colors.transparent,
          TeenModeSettingPage(
            openState: "1",
            pwd: entity?.password ?? "",
            comFrom: "main",
            hideBack: true,
          ),
        );
      }
    } else {
      bool isOpen = await checkCurrentTeenModeStatus();
      if (isOpen) {
        currentIndex.value = 2;
        update();
      }
    }
  }

  Future<bool> checkCurrentTeenModeStatus({bool? hideBack}) async {
    TeenModeStatusEntity? entity;
    if (teenModeStatusEntity != null) {
      entity = teenModeStatusEntity;
    } else {
      entity = await ApiService().getTeenModeStatus(showLoading: false);
    }
    if (entity?.toggle == "1") {
      Get.dialog(
        barrierDismissible: false,
        useSafeArea: false,
        barrierColor: Colors.transparent,
        TeenModeSettingPage(
          openState: "1",
          pwd: entity?.password ?? "",
          comFrom: "main",
          hideBack: hideBack,
        ),
      );
      return true;
    }
    return false;
  }
}
