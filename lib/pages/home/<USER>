import 'package:carousel_slider/carousel_slider.dart';
import 'package:dada/common/values/colors.dart';
import 'package:dada/common/values/enums.dart';
import 'package:dada/common/values/text_styles.dart';
import 'package:dada/common/values/themes.dart';
import 'package:dada/components/widgets/empty_widget.dart';
import 'package:dada/components/widgets/label_item_widget.dart';
import 'package:dada/configs/app_config.dart';
import 'package:dada/generated/assets.dart';
import 'package:dada/generated/l10n.dart';
import 'package:dada/model/chat_room_info_entity.dart';
import 'package:dada/model/user_info_entity.dart';
import 'package:dada/pages/home/<USER>';
import 'package:dada/routers/router.dart';
import 'package:dada/services/floating/global_floating_manager.dart';
import 'package:dada/services/network/api_service.dart';
import 'package:dada/services/trtc/chat_room_rtc_manager.dart';
import 'package:dada/utils/image_utils.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

class HomePage extends StatefulWidget {
  const HomePage({super.key});

  @override
  State<HomePage> createState() => _HomePageState();
}

class _HomePageState extends State<HomePage> {
  final controller = Get.put(HomeController());

  @override
  void initState() {
    super.initState();

    controller.data = controller.initData;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.themeData.scaffoldBackgroundColor,
      body: Stack(
        children: [
          ///背景
          Container(
            height: 251.h,
            decoration: const BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
                colors: [Color(0xFFADF0B0), Color(0x00E4FFBA)],
              ),
            ),
          ),
          Container(
            padding: EdgeInsets.only(top: ScreenUtil().statusBarHeight + 13.h),
            child: Column(
              children: [
                ///搜索
                _buildSearch(),

                Expanded(
                  child: CustomScrollView(
                    controller: ScrollController(),
                    slivers: [
                      ///顶部卡片
                      SliverToBoxAdapter(
                        child: _buildHeadCard(),
                      ),

                      ///表头
                      SliverToBoxAdapter(
                        child: _buildListHeader(),
                      ),

                      ///推荐列表
                      SliverPadding(
                        padding: EdgeInsets.only(
                            top: 17.h, left: 15.w, right: 15.w, bottom: 20.h),
                        sliver: _buildList(),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSearch() {
    return Row(
      children: [
        ///搜索框
        GestureDetector(
          onTap: () {
            Get.toNamed(GetRouter.searchRoom);
          },
          child: Container(
            width: 250.w,
            height: 35.h,
            margin: EdgeInsets.only(left: 22.w),
            padding: EdgeInsets.only(left: 10.w),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(35.h / 2),
              color: AppTheme.themeData.colorScheme.surface,
            ),
            child: Row(
              children: [
                ImageUtils.getImage(
                    Assets.imagesHomeSearchIcon, 13.33.w, 13.33.w),
                SizedBox(width: 8.w),
                Text(
                  "请输入房间号或话题关键字",
                  style:
                      TextStyles.normal(14.sp, c: AppTheme.themeData.hintColor),
                ),
              ],
            ),
          ),
        ),

        const Spacer(),

        ///签到
        GestureDetector(
          onTap: () {
            controller.showSignInDialog();
          },
          child: Container(
            margin: EdgeInsets.only(right: 10.w),
            width: 78.w,
            height: 35.h,
            padding: EdgeInsets.only(left: 8.w),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(35.h / 2),
              color: const Color(0xFF7DB37C),
            ),
            child: Row(
              children: [
                ImageUtils.getImage(Assets.imagesHomeSignIcon, 24.w, 24.w),
                SizedBox(width: 7.w),
                Text(
                  S.current.sign,
                  style: TextStyles.normal(14.sp,
                      c: AppTheme.themeData.colorScheme.primary),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildHeadCard() {
    return Container(
      margin: EdgeInsets.only(top: 15.w, left: 15.w, right: 15.w),
      child: Column(
        children: [
          Padding(
            padding: EdgeInsets.only(left: 7.w, bottom: 15.h),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  "场景",
                  style: TextStyles.common(18.sp, AppColors.colorFF2D6D0B),
                ),
                GestureDetector(
                  behavior: HitTestBehavior.opaque,
                  onTap: () {
                    Get.toNamed(GetRouter.chatRoomList);
                  },
                  child: Row(
                    children: [
                      Text(
                        "房间列表",
                        style:
                            TextStyles.common(16.sp, AppColors.colorFF666666),
                      ),
                      Padding(
                        padding: EdgeInsets.only(left: 10.w, right: 21.w),
                        child: ImageUtils.getImage(
                            Assets.imagesCommonListMore, 8.w, 14.h),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              ///一起做件事
              GestureDetector(
                onTap: () {
                  Get.toNamed(GetRouter.todoTogether);
                },
                child: Container(
                  width: 165.w,
                  height: 250.h,
                  decoration: const BoxDecoration(
                    image: DecorationImage(
                      image: AssetImage(Assets.imagesHomeHeadTodoTogether),
                    ),
                  ),
                ),
              ),

              SizedBox(width: 13.w),

              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  ///去茶壶
                  GestureDetector(
                    onTap: () async {
                      if (GlobalFloatingManager().currentIsShowMiniWindow()) {
                        return;
                      }
                      TRTCManager.sharedInstance.resetMicAndSpeakerState();
                      ChatRoomInfoEntity? roomInfo =
                          await ApiService().getRandomMatchingRoom(roomType: 2);
                      if (roomInfo != null) {
                        Get.toNamed(
                          GetRouter.chatRoomDetail,
                          parameters: {
                            "roomId": roomInfo.roomNo!,
                            "roomType": "2"
                          },
                        );
                      }
                    },
                    child: Container(
                      width: 165.w,
                      height: 120.h,
                      decoration: const BoxDecoration(
                        image: DecorationImage(
                          image: AssetImage(Assets.imagesHomeHeadCardTeapot),
                        ),
                      ),
                      child: Stack(
                        children: [
                          Positioned(
                            left: 9.w,
                            bottom: 5.h,
                            child: GestureDetector(
                              behavior: HitTestBehavior.opaque,
                              onTap: () {
                                if (GlobalFloatingManager()
                                    .currentIsShowMiniWindow()) {
                                  return;
                                }
                                Get.toNamed(GetRouter.createRoom,
                                    arguments: ChatRoomType.teapot);
                              },
                              child: SizedBox(width: 56.w, height: 42.h),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),

                  SizedBox(
                    height: 10.h,
                  ),

                  ///去市集
                  GestureDetector(
                    onTap: () async {
                      if (GlobalFloatingManager().currentIsShowMiniWindow()) {
                        return;
                      }
                      TRTCManager.sharedInstance.resetMicAndSpeakerState();
                      ChatRoomInfoEntity? roomInfo =
                          await ApiService().getRandomMatchingRoom(roomType: 1);
                      if (roomInfo != null) {
                        Get.toNamed(
                          GetRouter.chatRoomDetail,
                          parameters: {
                            "roomId": roomInfo.roomNo!,
                            "roomType": "1"
                          },
                        );
                      }
                    },
                    child: Container(
                      width: 165.w,
                      height: 55.h,
                      decoration: const BoxDecoration(
                        image: DecorationImage(
                          image: AssetImage(Assets.imagesHomeHeadCardFairSmall),
                        ),
                      ),
                      child: Stack(
                        alignment: Alignment.centerLeft,
                        children: [
                          Positioned(
                            left: 10.w,
                            child: GestureDetector(
                              onTap: () {
                                if (GlobalFloatingManager()
                                    .currentIsShowMiniWindow()) {
                                  return;
                                }
                                Get.toNamed(GetRouter.createRoom,
                                    arguments: ChatRoomType.fair);
                              },
                              child: ImageUtils.getImage(
                                  Assets.imagesHomeHeadFairCardSmallCreateRoom,
                                  40.w,
                                  40.w),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                  SizedBox(height: 10.h),

                  ///扩列
                  GestureDetector(
                    onTap: () {
                      controller.sendGreetMsg();
                    },
                    child: Container(
                      width: 165.w,
                      height: 55.h,
                      decoration: const BoxDecoration(
                        image: DecorationImage(
                          image: AssetImage(Assets.imagesHomeHeadSuiji),
                          fit: BoxFit.cover,
                        ),
                      ),
                      child: Container(),
                    ),
                  ),
                ],
              ),
            ],
          ),
          _buildBanner(),
          Padding(
            padding: EdgeInsets.only(top: 17.h),
            child: Row(
              children: [
                _buildMatchTeamCard(),
                _buildWorldChatCard(),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildBanner() {
    return Obx(() {
      return Container(
        margin: EdgeInsets.only(top: 17.h),
        height: 80.h,
        decoration: BoxDecoration(
          image: controller.bannerList.isNotEmpty == true
              ? null
              : const DecorationImage(
                  image: AssetImage(Assets.imagesHomeBanner),
                ),
          color: AppColors.colorFFE1E1E1,
          borderRadius: BorderRadius.circular(10.r),
        ),
        child: CarouselSlider(
          options: CarouselOptions(
              height: 80.h,
              aspectRatio: (ScreenUtil().screenWidth - 15.w * 2) / 80.h,
              viewportFraction: 1.05,
              autoPlayInterval: const Duration(seconds: 7),
              autoPlay: controller.bannerList.length > 1),
          items: controller.bannerList.map((e) {
            int index = controller.bannerList.indexOf(e);
            return GestureDetector(
              onTap: () {},
              child: ClipRRect(
                borderRadius: BorderRadius.circular(10.r),
                child: ImageUtils.getImage(
                  e.url ?? "",
                  ScreenUtil().screenWidth - 15.w * 2,
                  80.h,
                  fit: BoxFit.cover,
                  placeholder: index == 0 ? Assets.imagesHomeBanner : null,
                  showPlaceholder: index == 0,
                ),
              ),
            );
          }).toList(),
        ),
      );
    });
  }

  Widget _buildMatchTeamCard() {
    return GestureDetector(
      onTap: () {
        Get.toNamed(GetRouter.matchAssemblePlace);
      },
      child: SizedBox(
        width: 165.w,
        height: 60.h,
        child: ImageUtils.getImage(Assets.imagesHomeMatchCard, 165.w, 60.h,
            fit: BoxFit.fill),
      ),
    );
  }

  Widget _buildWorldChatCard() {
    return Container(
      width: 165.w,
      height: 60.h,
      margin: EdgeInsets.only(left: 12.w),
      child: Stack(
        children: [
          CarouselSlider(
            carouselController: controller.carouselController,
            options: CarouselOptions(
              height: 60.h,
              aspectRatio: 165.w / 60.h,
              viewportFraction: 1,
            ),
            items: [1, 2]
                .map(
                  (e) => GestureDetector(
                    onTap: () {
                      controller.gotoWorldGroupChat(e);
                    },
                    child: SizedBox(
                      width: 165.w,
                      height: 60.h,
                      child: ImageUtils.getImage(
                        e == 1
                            ? Assets.imagesHomeWorldChatCard1
                            : Assets.imagesHomeWorldChatCard2,
                        165.w,
                        60.h,
                        fit: BoxFit.fill,
                      ),
                    ),
                  ),
                )
                .toList(),
          ),
          Positioned(
            right: 0,
            bottom: 0,
            width: 28.w,
            height: 100.h,
            child: GestureDetector(
              onTap: () {
                controller.carouselController.nextPage();
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildListHeader() {
    return Container(
      margin: EdgeInsets.only(top: 17.h, left: 15.w),
      child: Text(
        "发现新伙伴",
        style: TextStyles.common(17.sp, AppColors.colorFF2D6D0B,
            w: FontWeight.w600),
      ),
    );
  }

  Widget _buildList() {
    return GetBuilder<HomeController>(
      init: controller,
      builder: (controller) {
        if (controller.data.isEmpty) {
          return SliverToBoxAdapter(
            child: Container(
              padding: EdgeInsets.only(top: 30.h),
              child: EmptyWidget(
                content: "暂无内容，点击刷新试试~",
                onTap: () {
                  controller.checkShouldReload();
                },
              ),
            ),
          );
        }
        return SliverList.separated(
          itemCount: controller.data.length,
          separatorBuilder: (context, index) {
            return Container(
              height: 10.h,
              color: Colors.transparent,
            );
          },
          itemBuilder: (context, index) {
            UserInfoEntity itemEntity = controller.data[index];
            return _buildListItem(itemEntity);
          },
        );
      },
    );
  }

  Widget _buildListItem(UserInfoEntity itemEntity) {
    return Container(
      height: 100.h,
      decoration: const BoxDecoration(
        image: DecorationImage(
          image: AssetImage(Assets.imagesHomeListItemBg),
          fit: BoxFit.cover,
        ),
      ),
      child: Stack(
        children: [
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Padding(
                padding: EdgeInsets.only(left: 15.w, top: 12.h),
                child: GestureDetector(
                  onTap: () {
                    if (itemEntity.id?.isNotEmpty == true) {
                      Get.toNamed(GetRouter.userProfile,
                          parameters: {"userId": itemEntity.id!});
                    }
                  },
                  child: ClipOval(
                    child: Container(
                      width: 60.w,
                      height: 60.w,
                      color: AppColors.colorFFE1E1E1,
                      child: ImageUtils.getImage(
                        itemEntity.avatar ?? "",
                        60.w,
                        60.w,
                        fit: BoxFit.cover,
                      ),
                    ),
                  ),
                ),
              ),
              Padding(
                padding: EdgeInsets.only(left: 10.w, top: 15.h),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      itemEntity.nickname ?? "-",
                      style: TextStyles.medium(16.sp),
                    ),
                    Padding(
                      padding: EdgeInsets.only(top: 3.h),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.start,
                        children: [
                          Text(
                            itemEntity.sex == null
                                ? "-"
                                : (itemEntity.sex == 0 ? "男" : "女"),
                            style: TextStyles.medium(12.sp,
                                c: AppColors.colorFF716D9C),
                          ),
                          Visibility(
                            visible: itemEntity.age != null,
                            child: Text(
                              " | ${itemEntity.age}岁",
                              style: TextStyles.medium(12.sp,
                                  c: AppColors.colorFF716D9C),
                            ),
                          ),
                          Visibility(
                            visible:
                                itemEntity.constellation?.isNotEmpty == true,
                            child: Text(
                              " | ${itemEntity.constellation}",
                              style: TextStyles.medium(12.sp,
                                  c: AppColors.colorFF716D9C),
                            ),
                          ),
                          Visibility(
                            visible:
                                _getUserRelationState(itemEntity.socialState)
                                        .isNotEmpty ==
                                    true,
                            child: Text(
                              " | ${_getUserRelationState(itemEntity.socialState)}",
                              style: TextStyles.medium(12.sp,
                                  c: AppColors.colorFF716D9C),
                            ),
                          ),
                        ],
                      ),
                    ),
                    Padding(
                      padding: EdgeInsets.only(top: 10.h),
                      child: Container(
                        height: 20.h,
                        constraints: BoxConstraints(
                          maxWidth: 250.w,
                        ),
                        child: itemEntity.labels?.isNotEmpty == true
                            ? ListView(
                                padding: EdgeInsets.zero,
                                scrollDirection: Axis.horizontal,
                                children: [
                                  Wrap(
                                    spacing: 5.w,
                                    children: itemEntity.labels!
                                        .map(
                                          (e) => LabelItemWidget(
                                            height: 20.h,
                                            minWidth: 50.w,
                                            editable: false,
                                            borderRadius: 250.h / 2,
                                            text: e.labelName ?? "",
                                            bgColor: AppColors.colorFFEDF3F9,
                                            textColor: AppColors.colorFF666666,
                                            fontSize: 12.sp,
                                          ),
                                        )
                                        .toList(),
                                  ),
                                ],
                              )
                            : Container(),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          Positioned(
            right: 10.w,
            top: 12.h,
            child: GestureDetector(
              onTap: () {
                if (itemEntity.id?.isNotEmpty == true) {
                  controller.gotoChat(itemEntity.id!);
                }
              },
              child: Container(
                width: 60.w,
                height: 25.h,
                alignment: Alignment.center,
                decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(25.h / 2),
                    gradient: const LinearGradient(
                        begin: Alignment.centerLeft,
                        end: Alignment.centerRight,
                        colors: [
                          AppColors.colorFF90E294,
                          AppColors.colorFF26BC2D,
                        ])),
                child: Text(
                  "找Ta聊",
                  style: TextStyles.common(12.sp, Colors.white),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  String _getUserRelationState(int? state) {
    switch (state) {
      case 1:
        return "冰冷";
      case 2:
        return "慢热";
      case 3:
        return "适中";
      case 4:
        return "热情";
      case 5:
        return "非常热情";
      default:
        return "";
    }
  }
}
