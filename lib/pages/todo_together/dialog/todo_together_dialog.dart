import 'package:dada/common/values/colors.dart';
import 'package:dada/common/values/text_styles.dart';
import 'package:dada/generated/assets.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class TodoTogetherDialog extends StatelessWidget {
  final String? title;
  final String? content;
  final String? leftBtnTitle;
  final String? rightBtnTitle;
  final Function()? onLeftBtnTap;
  final Function()? onRightBtnTap;

  const TodoTogetherDialog(
      {super.key,
      this.title,
      this.content,
      this.leftBtnTitle,
      this.rightBtnTitle,
      this.onLeftBtnTap,
      this.onRightBtnTap});

  @override
  Widget build(BuildContext context) {
    return Dialog(
      backgroundColor: Colors.transparent,
      surfaceTintColor: Colors.transparent,
      insetPadding: EdgeInsets.zero,
      child: SizedBox(
        width: 278.w,
        height: 267.h,
        child: Stack(
          children: [
            Positioned.fill(
                child: Image.asset(Assets.imagesTodoTogetherDialogBac)),
            Column(
              mainAxisAlignment: MainAxisAlignment.end,
              // mainAxisSize: MainAxisSize.min,
              children: [
                Padding(
                  padding: EdgeInsets.only(top: 10.h),
                  child: Text(
                    content ?? "",
                    style: TextStyles.medium(18.sp, c: Colors.white),
                  ),
                ),
                SizedBox(height: 25.h),
                Padding(
                  padding:
                      EdgeInsets.symmetric(horizontal: 30.w, vertical: 20.h),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      GestureDetector(
                        onTap: onLeftBtnTap,
                        child: Container(
                          width: 103.w,
                          height: 40.h,
                          alignment: Alignment.center,
                          decoration: BoxDecoration(
                            color: const Color(0xFF7A71C7),
                            borderRadius: BorderRadius.circular(8.r),
                            border: Border.all(
                                color: const Color(0xFFE3E3E3), width: 1),
                          ),
                          child: Text(
                            leftBtnTitle ?? "",
                            style: TextStyles.normal(16.sp, c: Colors.white),
                          ),
                        ),
                      ),
                      GestureDetector(
                        onTap: onRightBtnTap,
                        child: Container(
                          width: 103.w,
                          height: 40.h,
                          alignment: Alignment.center,
                          decoration: BoxDecoration(
                            color: Colors.transparent,
                            borderRadius: BorderRadius.circular(8.r),
                            border: Border.all(
                                color: const Color(0xFF9D93DB), width: 1),
                          ),
                          child: Text(
                            rightBtnTitle ?? "",
                            style: TextStyles.normal(16.sp, c: Colors.white),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
