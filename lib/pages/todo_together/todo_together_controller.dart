import 'package:flutter/material.dart';
import 'package:get/get.dart';

class TodoTogetherController extends GetxController {
  FocusNode focusNode = FocusNode();
  RxBool isFocusNode = false.obs;
  RxBool fullRoomHidden = false.obs; //true-过滤不显示已满房间 false-显示已满房间
  RxString searchKey = ''.obs;

  @override
  void onReady() {
    super.onReady();
  }

  @override
  void onInit() {
    super.onInit();
    focusNode.addListener(() {
      if (focusNode.hasFocus) {
        isFocusNode.value = true;
      } else {
        isFocusNode.value = false;
      }
    });
  }

  @override
  void onClose() {
    super.onClose();
    focusNode.dispose();
  }
}
