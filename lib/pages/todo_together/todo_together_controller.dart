import 'dart:async';

import 'package:dada/common/values/enums.dart';
import 'package:dada/pages/home/<USER>';
import 'package:dada/pages/todo_together/list/todo_together_list_controller.dart';
import 'package:dada/services/event_bus/event_bus_engine.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class TodoTogetherController extends GetxController {
  FocusNode focusNode = FocusNode();
  RxBool isFocusNode = false.obs;
  RxBool fullRoomHidden = true.obs; //true-显示已满房间 false-过滤不显示已满房间
  RxString searchKey = ''.obs;

  RxString unReadTaskId = "".obs;

  late StreamSubscription myEventSub;

  @override
  void onReady() {
    super.onReady();
  }

  @override
  void onInit() {
    super.onInit();
    if (Get.find<HomeController>().todoTogetherUnreadId.value.isNotEmpty) {
      unReadTaskId.value =
          Get.find<HomeController>().todoTogetherUnreadId.value;
    }
    myEventSub = EventBusEngine.eventBus.on().listen((event) {
      EventEntity entity = event;
      if (entity.event == BusEvent.receiveTodoTogetherMsg) {
        unReadTaskId.value = entity.message ?? "";
      }
    });
    focusNode.addListener(() {
      if (focusNode.hasFocus) {
        isFocusNode.value = true;
      } else {
        isFocusNode.value = false;
      }
    });
  }

  @override
  void onClose() {
    myEventSub.cancel();
    focusNode.dispose();
    super.onClose();
  }
}
