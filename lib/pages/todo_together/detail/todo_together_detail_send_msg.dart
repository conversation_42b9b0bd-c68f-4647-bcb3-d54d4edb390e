import 'package:dada/common/values/enums.dart';
import 'package:dada/common/values/text_styles.dart';
import 'package:dada/components/widgets/image_browser.dart';
import 'package:dada/components/widgets/permission_use_desc_dialog.dart';
import 'package:dada/generated/assets.dart';
import 'package:dada/model/todo_together_detail_entity.dart';
import 'package:dada/pages/report/report_page.dart';
import 'package:dada/pages/todo_together/detail/bottom_buttons.dart';
import 'package:dada/pages/todo_together/detail/bottom_tips.dart';
import 'package:dada/pages/todo_together/detail/todo_together_detail_controller.dart';
import 'package:dada/pages/todo_together/dialog/todo_together_rich_text.dart';
import 'package:dada/routers/router.dart';
import 'package:dada/services/user/user_service.dart';
import 'package:dada/utils/audio_player_utils.dart';
import 'package:dada/utils/audio_recorder.dart';
import 'package:dada/utils/image_utils.dart';
import 'package:dada/utils/toast_utils.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:image_picker/image_picker.dart';
import 'package:permission_handler/permission_handler.dart';

class TodoTogetherDetailSendMsg extends StatefulWidget {
  const TodoTogetherDetailSendMsg(
      {super.key, this.audioUrl, this.isSend = true});

  // 可能是网络语音URL或本地录音路径
  final String? audioUrl;

  final bool isSend; // true-发送消息 false-查看消息

  @override
  State<TodoTogetherDetailSendMsg> createState() =>
      _TodoTogetherDetailSendMsgState();
}

class _TodoTogetherDetailSendMsgState extends State<TodoTogetherDetailSendMsg>
    with SingleTickerProviderStateMixin {
  final TextEditingController _textController = TextEditingController();
  final FocusNode _focusNode = FocusNode();
  final audioRecorder = AudioRecorder();
  final audioPlayer = AudioPlayerUtils();

  // 是否有录音
  bool hasRecording = false;
  // 当前录音路径或URL
  String? currentAudioPath;
  // 录音时长
  int audioDuration = 0;
  // 是否正在播放
  bool isPlaying = false;

  final TodoTogetherDetailController controller =
      Get.find<TodoTogetherDetailController>();
  TodoTogetherDetailStage5? model5;
  TodoTogetherDetailStage6? model6;

  bool get myComplete => controller.detail?.stage5?.myEndMsg != null;

  @override
  void initState() {
    super.initState();

    model5 = controller.detail?.stage5;
    model6 = controller.detail?.stage6;

    _getSendInfo();

    audioRecorder.maxLength = 30; // 设置最大录音时长为30秒
    audioRecorder.minLength = 3; // 设置最小录音时长为3秒

    // 初始化音频播放器
    audioPlayer.onInit();
    audioPlayer.stateStream?.listen((event) {
      if (event == AudioPlayerState.playing) {
        setState(() {
          isPlaying = true;
        });
      } else {
        setState(() {
          isPlaying = false;
        });
      }
    });

    // 检查是否有初始音频URL
    if (widget.audioUrl != null && widget.audioUrl!.isNotEmpty) {
      currentAudioPath = widget.audioUrl;
      hasRecording = true;
      _loadAudioDuration();
    }
  }

  Future<void> _loadAudioDuration() async {
    if (currentAudioPath != null) {
      Duration? duration = await audioPlayer.setUrl(currentAudioPath!);
      if (duration != null) {
        setState(() {
          audioDuration = duration.inSeconds;
        });
      }
    }
  }

  @override
  void dispose() {
    _textController.dispose();
    _focusNode.dispose();
    audioRecorder.dispose();
    audioPlayer.dispose();
    super.dispose();
  }

  void _startRecording() async {
    // 如果正在播放，先停止播放
    if (isPlaying) {
      audioPlayer.stop();
    }

    var status = await Permission.microphone.status;
    if (status.isDenied) {
      // 请求权限
      status = await Permission.microphone.request();
    }

    if (status.isGranted) {
      // 权限已授予
      audioRecorder.startRecording();
    } else if (status.isPermanentlyDenied) {
      // 用户永久拒绝权限
      ToastUtils.showDialog(
        content: "录音权限未开启，要授予权限才能使用录音功能",
        confirmBtnTitle: "去开启",
        onConfirm: () {
          openAppSettings();
        },
      );
    } else {
      // 其他情况
      ToastUtils.showToast("录音权限请求被拒绝");
    }
  }

  void _stopRecording() async {
    await audioRecorder.stopRecording();
    if ((audioRecorder.duration ?? 0) < audioRecorder.minLength) {
      audioRecorder.reset();
      ToastUtils.showToast("录音时间太短，请重新录制");
    } else {
      // 录音成功
      setState(() {
        hasRecording = true;
        currentAudioPath = audioRecorder.filePath;
        controller.audioPath.value = currentAudioPath ?? "";
        audioDuration = audioRecorder.duration ?? 0;
      });
    }
  }

  String? _getImgPath() {
    String? path;
    if (widget.isSend) {
      if (myComplete) {
        path = model5?.myEndMsg?.imgUrl;
      } else {
        path = controller.sendMsgImg.value.isEmpty
            ? Assets.imagesTodoTogetherDetailAddPicture
            : controller.sendMsgImg.value;
      }
    } else {
      path = model6?.otherEndMsg?.imgUrl;
    }
    return path;
  }

  void _getSendInfo() {
    if (widget.isSend) {
      if (myComplete) {
        currentAudioPath = model5?.myEndMsg?.audioUrl;
        _textController.text = model5?.myEndMsg?.msg ?? '';
      }
    } else {
      currentAudioPath = model6?.otherEndMsg?.audioUrl;
      _textController.text = model6?.otherEndMsg?.msg ?? '';
    }
    setState(() {
      hasRecording = currentAudioPath != null;
    });
  }

  @override
  Widget build(BuildContext context) {
    model5 = controller.detail?.stage5;
    model6 = controller.detail?.stage6;
    String richTextContent = widget.isSend
        ? '恭喜你们,双方都完成了这件事,呼呼,可真不容易呀～接下来,你可以向对方发送一次信息了哦!比如对这件事的看法,过程中的趣事,对对方想说的…\n这是双方对一起做这件事的交流机会,也是一次了解对方的机会哈~'
        : '这是对方对这一次一起${controller.detail?.content}的评价哦,可以看看ta是不是你想进一步认识的人呢?';
    String richTextTip = widget.isSend
        ? '可以发一次信息了哦！可以是做这件事的进度（比如做完了,或者时间紧迫做了1/3等）,可以是对这件事的感悟（正式、感慨、抽象、搞笑等均可哦~\n同一个事,不同的人有不同的观点和态度,哪怕是个小事也能信口胡诌玩抽象哈哈,这里最能体现一个人的生活态度哦）,也可以是对这件事本身的评价（比如一起看了一个小说,综艺,电影,肯定会有些想法）等等等等~'
        : '';
    return Column(
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        SizedBox(height: 39.h),
        TodoTogetherRichText(content: richTextContent, tipContent: richTextTip),
        SizedBox(height: 32.h),
        Container(
          width: 310.w,
          height: 147.h,
          decoration: BoxDecoration(
              color: Colors.white.withOpacity(0.57),
              border: Border.all(width: 1, color: Colors.white),
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(12.r),
                topRight: Radius.circular(12.r),
                bottomLeft: Radius.circular(12.r),
              )),
          child: Stack(
            children: [
              Padding(
                padding: EdgeInsets.all(12.r),
                child: TextField(
                  enabled: widget.isSend && !myComplete,
                  controller: _textController,
                  focusNode: _focusNode,
                  maxLines: 5,
                  style:
                      TextStyles.common(13.sp, Colors.white, h: 18.sp / 13.sp),
                  cursorColor: Colors.white,
                  decoration: InputDecoration(
                    border: InputBorder.none,
                    hintText: widget.isSend ? '输入文字' : '',
                    hintStyle: TextStyles.common(13.sp, Colors.white),
                  ),
                ),
              ),
              _getImgView(),
            ],
          ),
        ),
        if (widget.isSend) SizedBox(height: 16.h),
        if (!widget.isSend)
          GestureDetector(
            behavior: HitTestBehavior.opaque,
            onTap: () {
              Get.to(() => ReportPage(
                  reportType: ReportType.chatMsg,
                  userId: controller.detail?.otherUserId));
            },
            child: Padding(
              padding: EdgeInsets.symmetric(vertical: 8.h, horizontal: 42.w),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  ImageUtils.getImage(
                      Assets.imagesTodoTogetherDetailError, 13, 13),
                  // Text('帮助', style: TextStyles.common(12.sp, Colors.white)),
                ],
              ),
            ),
          ),
        if (widget.isSend) _getAudioView(),
        if (!widget.isSend && model6?.otherEndMsg?.audioUrl != null)
          _getAudioView(),
        BottomTips(
          visible: !widget.isSend,
          content: '据研究,人们在相熟前一起做过2-3件以上的事的话,关系会更牢固哦~',
          verticalPadding: 34.h,
        ),
        BottomButtons(
          visible: widget.isSend &&
              controller.detail?.stage == 5 &&
              model5?.myEndMsg == null &&
              controller.fromType == TodoTogetherListType.all,
          centerImg: Assets.imagesTodoTogetherDetailSend,
          centerOnTap: () {
            final bool hasText = _textController.text.trim().isNotEmpty;
            final bool hasImage = controller.sendMsgImg.value.isNotEmpty;
            final bool hasAudio = hasRecording;
            if (!hasText && !hasImage && !hasAudio) {
              ToastUtils.showToast('至少包含一种信息（文字、图片或语音）');
              return;
            }
            controller.completeToMsg(_textController.text);
          },
        ),
        BottomTips(
            content: "请等待对方发送的消息吧~",
            visible: controller.detail?.stage == 5 &&
                widget.isSend &&
                model5?.myEndMsg != null),
        if (widget.isSend &&
            controller.detail?.stage == 5 &&
            model5?.myEndMsg != null)
          SizedBox(height: 77.h),
        BottomButtons(
          visible: !widget.isSend &&
              (model6?.isMyEndSelect == null) &&
              controller.fromType == TodoTogetherListType.all,
          verticalPadding: 34.h,
          leftImg: Assets.imagesTodoTogetherDetailEnd,
          leftDes: '相遇很开心,挥手说再见,下次再会!',
          centerImg: Assets.imagesTodoTogetherDetailAddFriendBtn,
          centerDes: '就是ta了,结识ta!',
          rightImg: Assets.imagesTodoTogetherDetailAgainBtn,
          rightDes: '同一个人继续发起新事',
          leftOnTap: () {
            _bottomButtonsAction(1);
          },
          centerOnTap: () {
            _bottomButtonsAction(2);
          },
          rightOnTap: () {
            _bottomButtonsAction(3);
          },
        ),
        BottomTips(
            content: '已确认,请等待对方作出选择',
            visible: !widget.isSend &&
                (model6?.isMyEndSelect != null &&
                    model6?.isOtherEndSelect == null)),
        SizedBox(height: 30.h),
        ImageUtils.getImage(Assets.imagesTodoTogetherDetailLine,
            ScreenUtil().screenWidth, 13.h),
        SizedBox(height: 50.h)
      ],
    );
  }

  Widget _getImgView() {
    return Positioned(
      right: 12.w,
      bottom: 6.h,
      child: GestureDetector(
        onTap: () async {
          if (widget.isSend) {
            if (myComplete) {
              ImageUtils.showImageBrowser(ImageBrowserArgs(
                [
                  HeroTagName.todoTogetherOtherImg.of(
                      model5?.myEndMsg?.imgUrl ??
                          'todo_together_detail_send_msg'),
                ],
                [model5?.myEndMsg?.imgUrl ?? ''],
              ));
            } else {
              PermissionStatus status = await Permission.photos.status;
              if (status.isGranted || status.isDenied) {
                if (GetPlatform.isAndroid) {
                  ToastUtils.showTopDialog(
                    barrierDismissible: false,
                    child: const PermissionUseDescDialog(
                        title: "相册权限说明", desc: "获取图片用于上传图片"),
                  );
                }
                status = await Permission.photos.request();
                if (Get.isDialogOpen == true) {
                  Get.back();
                }
                if (status != PermissionStatus.granted) {
                  return;
                }
              }

              XFile? imageFile =
                  await ImagePicker().pickImage(source: ImageSource.gallery);
              setState(() {
                controller.sendMsgImg.value = imageFile?.path ?? "";
              });
            }
          } else {
            ImageUtils.showImageBrowser(ImageBrowserArgs(
              [
                HeroTagName.todoTogetherOtherImg.of(
                    model6?.otherEndMsg?.imgUrl ??
                        'todo_together_detail_send_msg'),
              ],
              [model6?.otherEndMsg?.imgUrl ?? ''],
            ));
          }
        },
        child: _getImgPath() != null
            ? Hero(
                tag: HeroTagName.todoTogetherOtherImg.of((widget.isSend
                        ? model5?.myEndMsg?.imgUrl
                        : model6?.otherEndMsg?.imgUrl) ??
                    'todo_together_detail_send_msg'),
                child: ImageUtils.getImage(_getImgPath()!, 24.w, 24.w),
              )
            : Container(),
      ),
    );
  }

  Widget _getAudioView() {
    return GetBuilder<AudioRecorder>(
      init: audioRecorder,
      global: false,
      builder: (recorder) {
        return GestureDetector(
          onTap: () {
            if (widget.isSend) {
              if (myComplete) {
                if (isPlaying) {
                  audioPlayer.stop();
                } else {
                  audioPlayer.setUrl(currentAudioPath!);
                  audioPlayer.play();
                }
              } else {
                if (hasRecording) {
                  if (isPlaying) {
                    audioPlayer.stop();
                  } else {
                    audioPlayer.setUrl(currentAudioPath!);
                    audioPlayer.play();
                  }
                } else {
                  if (recorder.status == AudioRecorderStatus.idle ||
                      recorder.status == AudioRecorderStatus.stop) {
                    _startRecording();
                  } else if (recorder.status == AudioRecorderStatus.recording) {
                    _stopRecording();
                  }
                }
              }
            } else {
              if (isPlaying) {
                audioPlayer.stop();
              } else {
                audioPlayer.setUrl(currentAudioPath!);
                audioPlayer.play();
              }
            }
          },
          child: Container(
            width: 310.w,
            height: 48.h,
            decoration: BoxDecoration(
              color: Colors.white.withOpacity(0.57),
              border: Border.all(width: 1, color: Colors.white),
              borderRadius: BorderRadius.only(
                bottomLeft: Radius.circular(12.r),
                topLeft: Radius.circular(12.r),
                topRight: Radius.circular(12.r),
              ),
            ),
            child: Row(
              children: [
                SizedBox(width: 16.w),
                if (widget.isSend)
                  hasRecording
                      ? ImageUtils.getImage(
                          Assets.imagesTodoTogetherDetailAudioPlay,
                          17.w,
                          20.h,
                        )
                      : ImageUtils.getImage(
                          Assets.imagesTodoTogetherDetailMic, 17.w, 20.h),
                if (!widget.isSend)
                  ImageUtils.getImage(
                      Assets.imagesTodoTogetherDetailAudioPlay, 17.w, 20.h),
                SizedBox(width: 23.w),
                ImageUtils.getImage(
                  Assets.imagesTodoTogetherDetailWave,
                  hasRecording && !isPlaying ? 150.w : 180.w,
                  22.h,
                  color: recorder.status == AudioRecorderStatus.recording ||
                          isPlaying
                      ? Colors.white
                      : Colors.white.withOpacity(0.5),
                ),
                SizedBox(width: 16.w),
                if (widget.isSend)
                  Expanded(
                    child: Text(
                      hasRecording
                          ? "$audioDuration秒"
                          : recorder.status == AudioRecorderStatus.recording
                              ? "${recorder.duration ?? 0}秒"
                              : "",
                      style: TextStyles.common(14.sp, Colors.white),
                    ),
                  ),
                if (hasRecording && !isPlaying && model5?.myEndMsg == null)
                  GestureDetector(
                    onTap: () {
                      setState(() {
                        hasRecording = false;
                        currentAudioPath = null;
                        audioDuration = 0;
                      });
                      _startRecording();
                    },
                    child: Padding(
                      padding: EdgeInsets.only(right: 16.w),
                      child: Text(
                        "重录",
                        style: TextStyles.common(14.sp, Colors.white),
                      ),
                    ),
                  )
                else
                  SizedBox(width: 16.w),
              ],
            ),
          ),
        );
      },
    );
  }

  void _bottomButtonsAction(int type) {
    if (type == 1) {
      controller.endSelect("1");
    } else if (type == 2) {
      if (!UserService().checkIsMonthCardUser()) {
        controller.showStopDialog(context,
            content: '此功能需要月卡权益，您的支持我们将全用来将产品做的更好哦~\n（在小屋-任务中，也可以免费获得月卡权益~)',
            rightText: "开通月卡", onRightBtnTap: () {
          Get.back();
          Get.toNamed(GetRouter.rechargeMonthCard);
        });
      } else {
        controller.endSelect('2');
      }
    } else {
      controller.endSelect('3');
    }
  }
}
