import 'package:dada/common/values/text_styles.dart';
import 'package:dada/generated/assets.dart';
import 'package:dada/utils/audio_player_utils.dart';
import 'package:dada/utils/audio_recorder.dart';
import 'package:dada/utils/image_utils.dart';
import 'package:dada/utils/toast_utils.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:svgaplayer_flutter/svgaplayer_flutter.dart';

class TodoTogetherDetailSendMsg extends StatefulWidget {
  const TodoTogetherDetailSendMsg(
      {super.key, this.audioUrl, this.isSend = true});

  // 可能是网络语音URL或本地录音路径
  final String? audioUrl;

  final bool isSend; // true-发送消息 false-查看消息

  @override
  State<TodoTogetherDetailSendMsg> createState() =>
      _TodoTogetherDetailSendMsgState();
}

class _TodoTogetherDetailSendMsgState extends State<TodoTogetherDetailSendMsg>
    with SingleTickerProviderStateMixin {
  final TextEditingController _textController = TextEditingController();
  final FocusNode _focusNode = FocusNode();
  final audioRecorder = AudioRecorder();
  final audioPlayer = AudioPlayerUtils();

  // 是否有录音
  bool hasRecording = false;
  // 当前录音路径或URL
  String? currentAudioPath;
  // 录音时长
  int audioDuration = 0;
  // 是否正在播放
  bool isPlaying = false;

  @override
  void initState() {
    super.initState();
    audioRecorder.maxLength = 30; // 设置最大录音时长为30秒
    audioRecorder.minLength = 3; // 设置最小录音时长为3秒

    // 初始化音频播放器
    audioPlayer.onInit();
    audioPlayer.stateStream?.listen((event) {
      if (event == AudioPlayerState.playing) {
        setState(() {
          isPlaying = true;
        });
      } else {
        setState(() {
          isPlaying = false;
        });
      }
    });

    // 检查是否有初始音频URL
    if (widget.audioUrl != null && widget.audioUrl!.isNotEmpty) {
      currentAudioPath = widget.audioUrl;
      hasRecording = true;
      _loadAudioDuration();
    }
  }

  Future<void> _loadAudioDuration() async {
    if (currentAudioPath != null) {
      Duration? duration = await audioPlayer.setUrl(currentAudioPath!);
      if (duration != null) {
        setState(() {
          audioDuration = duration.inSeconds;
        });
      }
    }
  }

  @override
  void dispose() {
    _textController.dispose();
    _focusNode.dispose();
    audioRecorder.dispose();
    audioPlayer.dispose();
    super.dispose();
  }

  void _startRecording() async {
    // 如果正在播放，先停止播放
    if (isPlaying) {
      audioPlayer.stop();
    }

    var status = await Permission.microphone.status;
    if (status.isDenied) {
      // 请求权限
      status = await Permission.microphone.request();
    }

    if (status.isGranted) {
      // 权限已授予
      audioRecorder.startRecording();
    } else if (status.isPermanentlyDenied) {
      // 用户永久拒绝权限
      ToastUtils.showDialog(
        content: "录音权限未开启，要授予权限才能使用录音功能",
        confirmBtnTitle: "去开启",
        onConfirm: () {
          openAppSettings();
        },
      );
    } else {
      // 其他情况
      ToastUtils.showToast("录音权限请求被拒绝");
    }
  }

  void _stopRecording() async {
    await audioRecorder.stopRecording();
    if ((audioRecorder.duration ?? 0) < audioRecorder.minLength) {
      audioRecorder.reset();
      ToastUtils.showToast("录音时间太短，请重新录制");
    } else {
      // 录音成功
      setState(() {
        hasRecording = true;
        currentAudioPath = audioRecorder.filePath;
        audioDuration = audioRecorder.duration ?? 0;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        SizedBox(height: 39.h),
        Padding(
          padding: EdgeInsets.symmetric(horizontal: 30.w),
          child: Text(
            '恭喜你们，双方都完成了这件事，呼呼，可真不容易呀～接下来，你可以想对方发送一次信息了哦～',
            textAlign: TextAlign.center,
            style: TextStyles.common(13.sp, Colors.white, h: 18.sp / 13.sp),
          ),
        ),
        SizedBox(height: 32.h),
        Container(
          width: 310.w,
          height: 147.h,
          decoration: BoxDecoration(
              color: Colors.white.withOpacity(0.57),
              border: Border.all(width: 1, color: Colors.white),
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(12.r),
                topRight: Radius.circular(12.r),
                bottomLeft: Radius.circular(12.r),
              )),
          child: Stack(
            children: [
              Padding(
                padding: EdgeInsets.all(12.r),
                child: TextField(
                  controller: _textController,
                  focusNode: _focusNode,
                  maxLines: 5,
                  style:
                      TextStyles.common(13.sp, Colors.white, h: 18.sp / 13.sp),
                  cursorColor: Colors.white,
                  decoration: InputDecoration(
                    border: InputBorder.none,
                    hintText: '输入文字',
                    hintStyle: TextStyles.common(13.sp, Colors.white),
                  ),
                ),
              ),
              Positioned(
                right: 12.w,
                bottom: 6.h,
                child: GestureDetector(
                  onTap: () {
                    ToastUtils.showToast("添加图片");
                  },
                  child: ImageUtils.getImage(
                    Assets.imagesTodoTogetherDetailAddPicture,
                    24.w,
                    24.w,
                  ),
                ),
              ),
            ],
          ),
        ),
        if (widget.isSend) SizedBox(height: 16.h),
        if (!widget.isSend)
          GestureDetector(
            behavior: HitTestBehavior.opaque,
            onTap: () {
              ToastUtils.showToast("帮助");
            },
            child: Padding(
              padding: EdgeInsets.symmetric(vertical: 8.h, horizontal: 42.w),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  Icon(Icons.help_outline, color: Colors.white, size: 13),
                  Text('帮助', style: TextStyles.common(12.sp, Colors.white)),
                ],
              ),
            ),
          ),
        GetBuilder<AudioRecorder>(
          init: audioRecorder,
          global: false,
          builder: (recorder) {
            return GestureDetector(
              onTap: () {
                if (hasRecording) {
                  if (isPlaying) {
                    audioPlayer.stop();
                  } else {
                    audioPlayer.setUrl(currentAudioPath!);
                    audioPlayer.play();
                  }
                } else {
                  if (recorder.status == AudioRecorderStatus.idle ||
                      recorder.status == AudioRecorderStatus.stop) {
                    _startRecording();
                  } else if (recorder.status == AudioRecorderStatus.recording) {
                    _stopRecording();
                  }
                }
              },
              child: Container(
                width: 310.w,
                height: 48.h,
                decoration: BoxDecoration(
                  color: Colors.white.withOpacity(0.57),
                  border: Border.all(width: 1, color: Colors.white),
                  borderRadius: BorderRadius.only(
                    bottomLeft: Radius.circular(12.r),
                    topLeft: Radius.circular(12.r),
                    topRight: Radius.circular(12.r),
                  ),
                ),
                child: Row(
                  children: [
                    SizedBox(width: 16.w),
                    hasRecording
                        ? ImageUtils.getImage(
                            isPlaying
                                ? Assets.imagesTodoTogetherDetailAudioPlay
                                : Assets.imagesTodoTogetherDetailMic,
                            17.w,
                            20.h,
                          )
                        : ImageUtils.getImage(
                            Assets.imagesTodoTogetherDetailMic, 17.w, 20.h),
                    SizedBox(width: 23.w),
                    ImageUtils.getImage(
                      Assets.imagesTodoTogetherDetailWave,
                      hasRecording && !isPlaying ? 150.w : 180.w,
                      22.h,
                      color: recorder.status == AudioRecorderStatus.recording ||
                              isPlaying
                          ? Colors.white
                          : Colors.white.withOpacity(0.5),
                    ),
                    SizedBox(width: 16.w),
                    Expanded(
                      child: Text(
                        hasRecording
                            ? "$audioDuration秒"
                            : recorder.status == AudioRecorderStatus.recording
                                ? "${recorder.duration ?? 0}秒"
                                : "",
                        style: TextStyles.common(14.sp, Colors.white),
                      ),
                    ),
                    if (hasRecording && !isPlaying)
                      GestureDetector(
                        onTap: () {
                          setState(() {
                            hasRecording = false;
                            currentAudioPath = null;
                            audioDuration = 0;
                          });
                          _startRecording();
                        },
                        child: Padding(
                          padding: EdgeInsets.only(right: 16.w),
                          child: Text(
                            "重录",
                            style: TextStyles.common(14.sp, Colors.white),
                          ),
                        ),
                      )
                    else
                      SizedBox(width: 16.w),
                  ],
                ),
              ),
            );
          },
        ),
        if (!widget.isSend) SizedBox(height: 34.h),
        if (!widget.isSend)
          Padding(
            padding: EdgeInsets.symmetric(horizontal: 30.w),
            child: Text(
              '据研究，人们在相熟前一起做过2-3件以上的事的话，关系会更牢固哦～',
              textAlign: TextAlign.center,
              style: TextStyles.common(13.sp, Colors.white, h: 18.sp / 13.sp),
            ),
          ),
      ],
    );
  }
}
