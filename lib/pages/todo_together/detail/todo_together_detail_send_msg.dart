import 'package:dada/common/values/text_styles.dart';
import 'package:dada/generated/assets.dart';
import 'package:dada/utils/audio_recorder.dart';
import 'package:dada/utils/image_utils.dart';
import 'package:dada/utils/toast_utils.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:permission_handler/permission_handler.dart';

class TodoTogetherDetailSendMsg extends StatefulWidget {
  const TodoTogetherDetailSendMsg({super.key});

  @override
  State<TodoTogetherDetailSendMsg> createState() =>
      _TodoTogetherDetailSendMsgState();
}

class _TodoTogetherDetailSendMsgState extends State<TodoTogetherDetailSendMsg> {
  final TextEditingController _textController = TextEditingController();
  final FocusNode _focusNode = FocusNode();
  final audioRecorder = AudioRecorder();

  @override
  void initState() {
    super.initState();
    audioRecorder.maxLength = 30; // 设置最大录音时长为30秒
    audioRecorder.minLength = 3; // 设置最小录音时长为3秒
  }

  @override
  void dispose() {
    _textController.dispose();
    _focusNode.dispose();
    audioRecorder.dispose();
    super.dispose();
  }

  void _startRecording() async {
    var status = await Permission.microphone.status;
    if (status.isDenied) {
      // 请求权限
      status = await Permission.microphone.request();
    }

    if (status.isGranted) {
      // 权限已授予
      audioRecorder.startRecording();
    } else if (status.isPermanentlyDenied) {
      // 用户永久拒绝权限
      ToastUtils.showDialog(
        content: "录音权限未开启，要授予权限才能使用录音功能",
        confirmBtnTitle: "去开启",
        onConfirm: () {
          openAppSettings();
        },
      );
    } else {
      // 其他情况
      ToastUtils.showToast("录音权限请求被拒绝");
    }
  }

  void _stopRecording() async {
    await audioRecorder.stopRecording();
    if ((audioRecorder.duration ?? 0) < audioRecorder.minLength) {
      audioRecorder.reset();
      ToastUtils.showToast("录音时间太短，请重新录制");
    }
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        SizedBox(height: 39.h),
        Padding(
          padding: EdgeInsets.symmetric(horizontal: 30.w),
          child: Text(
            '恭喜你们，双方都完成了这件事，呼呼，可真不容易呀～接下来，你可以想对方发送一次信息了哦～',
            textAlign: TextAlign.center,
            style: TextStyles.common(13.sp, Colors.white, h: 18.sp / 13.sp),
          ),
        ),
        SizedBox(height: 32.h),
        Container(
          width: 310.w,
          height: 147.h,
          decoration: BoxDecoration(
              color: Colors.white.withOpacity(0.57),
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(12.r),
                topRight: Radius.circular(12.r),
                bottomLeft: Radius.circular(12.r),
              )),
          child: Stack(
            children: [
              // 输入框
              Padding(
                padding: EdgeInsets.all(12.r),
                child: TextField(
                  controller: _textController,
                  focusNode: _focusNode,
                  maxLines: 5,
                  style:
                      TextStyles.common(13.sp, Colors.white, h: 18.sp / 13.sp),
                  cursorColor: Colors.white,
                  decoration: InputDecoration(
                    border: InputBorder.none,
                    hintText: '输入文字',
                    hintStyle: TextStyles.common(13.sp, Colors.white),
                  ),
                ),
              ),
              // 右下角添加图片按钮
              Positioned(
                right: 12.w,
                bottom: 6.h,
                child: GestureDetector(
                  onTap: () {
                    // 添加图片的点击事件
                    ToastUtils.showToast("添加图片");
                  },
                  child: ImageUtils.getImage(
                    Assets.imagesTodoTogetherDetailAddPicture,
                    24.w,
                    24.w,
                  ),
                ),
              ),
            ],
          ),
        ),
        SizedBox(height: 16.h),
        // 语音录制容器
        GetBuilder<AudioRecorder>(
          init: audioRecorder,
          global: false,
          builder: (recorder) {
            return GestureDetector(
              onTap: () {
                if (recorder.status == AudioRecorderStatus.idle ||
                    recorder.status == AudioRecorderStatus.stop) {
                  _startRecording();
                } else if (recorder.status == AudioRecorderStatus.recording) {
                  _stopRecording();
                }
              },
              child: Container(
                width: 310.w,
                height: 48.h,
                decoration: BoxDecoration(
                  color: Colors.white.withOpacity(0.57),
                  borderRadius: BorderRadius.circular(24.r),
                ),
                child: Row(
                  children: [
                    SizedBox(width: 16.w),
                    ImageUtils.getImage(
                        Assets.imagesTodoTogetherDetailMic, 17.w, 20.h),
                    SizedBox(width: 23.w),
                    ImageUtils.getImage(
                      Assets.imagesTodoTogetherDetailWave,
                      200.w,
                      22.h,
                      color: recorder.status == AudioRecorderStatus.recording
                          ? Colors.white
                          : Colors.white.withOpacity(0.5),
                    ),
                    SizedBox(width: 16.w),
                    Expanded(
                      child: Text(
                        recorder.status == AudioRecorderStatus.recording
                            ? " ${recorder.duration ?? 0}s"
                            : "",
                        style: TextStyles.common(14.sp, Colors.white),
                      ),
                    ),
                    SizedBox(width: 16.w),
                  ],
                ),
              ),
            );
          },
        ),
      ],
    );
  }
}
