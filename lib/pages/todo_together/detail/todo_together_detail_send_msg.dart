import 'package:dada/common/values/text_styles.dart';
import 'package:dada/generated/assets.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class TodoTogetherDetailSendMsg extends StatelessWidget {
  const TodoTogetherDetailSendMsg({super.key});

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        SizedBox(height: 39.h),
        Padding(
          padding: EdgeInsets.symmetric(horizontal: 30.w),
          child: Text(
            '恭喜你们，双方都完成了这件事，呼呼，可真不容易呀～接下来，你可以想对方发送一次信息了哦～',
            textAlign: TextAlign.center,
            style: TextStyles.common(13.sp, Colors.white, h: 18.sp / 13.sp),
          ),
        ),
        SizedBox(height: 32.h),
        Container(
          width: 310.w,
          height: 147.h,
          decoration: BoxDecoration(
              color: Colors.white.withOpacity(0.57),
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(12.r),
                topRight: Radius.circular(12.r),
                bottomLeft: Radius.circular(12.r),
              )),
          child: Assets.imagesTodoTogetherDetailWave,
        ),
      ],
    );
  }
}
