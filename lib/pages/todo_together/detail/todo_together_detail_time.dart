import 'package:dada/common/values/text_styles.dart';
import 'package:dada/generated/assets.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class TodoTogetherDetailTime extends StatelessWidget {
  const TodoTogetherDetailTime({super.key});

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        SizedBox(height: 39.h),
        Container(
          margin: EdgeInsets.symmetric(horizontal: 24.w),
          height: 44.h,
          padding: EdgeInsets.symmetric(horizontal: 8.w),
          alignment: Alignment.center,
          // padding: EdgeInsets.symmetric(vertical: 11.h),
          decoration: BoxDecoration(
            color: Colors.white.withOpacity(0.34),
            borderRadius: BorderRadius.circular(8.r),
          ),
          child: Text(
            '一起吃个苹果一起吃个苹果一起吃个苹果一起吃个苹果一起吃个苹果',
            style: TextStyles.medium(16.sp, c: Colors.white)
                .copyWith(overflow: TextOverflow.ellipsis),
          ),
        ),
        SizedBox(height: 34.h),
        Padding(
          padding: EdgeInsets.symmetric(horizontal: 30.w),
          child: Text(
            '随着时间的流淌,自由自在做你想做的事吧～不过你还知道,有一个人,在某个时空在与你做着相同的事哦\n\n节奏自己把握好,当下就做,或者慢慢做,都随你心意哦～当然啦,不同的处事态度都是你自己各方面的体现哦～默默的做自己就好～',
            textAlign: TextAlign.center,
            style: TextStyles.common(13.sp, Colors.white, h: 18.sp / 13.sp),
          ),
        ),
        SizedBox(height: 35.h),
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.end,
          children: [
            _buildTimeItem(0),
            SizedBox(width: 9.w),
            _buildTimeItem(2),
            SizedBox(width: 4.w),
            Text(
              '天',
              style: TextStyles.common(12.sp, Colors.white),
            ),
            SizedBox(width: 4.w),
            _buildTimeItem(1),
            SizedBox(width: 9.w),
            _buildTimeItem(2),
            SizedBox(width: 4.w),
            Text(
              '小时',
              style: TextStyles.common(12.sp, Colors.white),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildTimeItem(int title) {
    return Container(
      width: 37.w,
      height: 40.h,
      alignment: Alignment.center,
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.4),
        borderRadius: BorderRadius.circular(4.r),
      ),
      child: Text(
        '$title',
        style: TextStyles.bold(20.sp, c: Colors.white),
      ),
    );
  }
}

class TodoTogetherDetailTimeAnswer extends StatelessWidget {
  const TodoTogetherDetailTimeAnswer({super.key});

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.end,
      children: [
        SizedBox(height: 17.h),
        Padding(
          padding: EdgeInsets.symmetric(horizontal: 60.w),
          child: Text(
            '对方戳了你一下，确认下～你还在吗？要记得做这件事哦～',
            textAlign: TextAlign.center,
            style: TextStyles.common(11.sp, Colors.white, h: 16.sp / 11.sp),
          ),
        ),
        SizedBox(height: 16.h),
        Container(
          margin: EdgeInsets.only(right: 60.w),
          child: GestureDetector(
            onTap: () {},
            child: Image.asset(Assets.imagesTodoTogetherDetailAnswer,
                width: 82.w, fit: BoxFit.fitWidth),
          ),
        ),
        SizedBox(height: 34.h),
        Padding(
          padding: EdgeInsets.symmetric(horizontal: 60.w),
          child: Text(
            '对方已经完成了这件事，如果你也完成了，记得点击我已完成按钮哦',
            textAlign: TextAlign.center,
            style: TextStyles.common(13.sp, Colors.white, h: 18.sp / 13.sp),
          ),
        ),
      ],
    );
  }
}
