import 'package:dada/common/values/text_styles.dart';
import 'package:dada/generated/assets.dart';
import 'package:dada/generated/l10n.dart';
import 'package:dada/pages/todo_together/detail/todo_together_detail_controller.dart';
import 'package:dada/utils/image_utils.dart';
import 'package:dada/utils/toast_utils.dart';
import 'package:dotted_border/dotted_border.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:image_picker/image_picker.dart';

class TodoTogetherUploadImage extends StatelessWidget {
  const TodoTogetherUploadImage({super.key});

  @override
  Widget build(BuildContext context) {
    final controller = Get.find<TodoTogetherDetailController>();

    return Column(
      children: [
        SizedBox(height: 39.h),
        Padding(
          padding: EdgeInsets.symmetric(horizontal: 34.w),
          child: Text(
            '起航!你们要开始一起XXX了哦，作为开始做这件事的证明，你们可以各自上传一张准备做这件事的图片哦~如果你想在事件完成后再发，或这个事件无需发图，也可以选择跳过哦~',
            textAlign: TextAlign.center,
            style: TextStyles.common(13.sp, Colors.white, h: 18.sp / 13.sp),
          ),
        ),
        SizedBox(height: 64.h),
        GestureDetector(
          behavior: HitTestBehavior.opaque,
          onTap: () {
            ToastUtils.showBottomSheet(
              [S.current.album, S.current.camera],
              onTap: (index) async {
                if (index == 0 || index == 1) {
                  XFile? imageFile = await ImagePicker().pickImage(
                      source: index == 0
                          ? ImageSource.gallery
                          : ImageSource.camera);
                  controller.uploadImg.value = imageFile?.path ?? "";
                }
              },
            );
          },
          child: DottedBorder(
            color: const Color(0xFFFBFBFB),
            dashPattern: [8, 8],
            radius: Radius.circular(8.r),
            strokeWidth: 2,
            child: SizedBox(
              width: 200,
              height: 200,
              child: controller.uploadImg.value.isEmpty
                  ? Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Image.asset(
                          Assets.imagesTodoTogetherDetailAddPicture,
                          color: Colors.white,
                        ),
                        SizedBox(height: 8.h),
                        Text(
                          '快来上传图片吧~',
                          style: TextStyles.common(14.sp, Colors.white),
                        )
                      ],
                    )
                  : Obx(() => ImageUtils.getImage(
                      controller.uploadImg.value, 200, 200)),
            ),
          ),
        ),
      ],
    );
  }
}
