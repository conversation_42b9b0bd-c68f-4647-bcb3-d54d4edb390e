import 'package:dada/common/values/text_styles.dart';
import 'package:dada/generated/assets.dart';
import 'package:dada/pages/todo_together/detail/todo_together_detail_controller.dart';
import 'package:dada/pages/todo_together/detail/todo_together_detail_other_img.dart';
import 'package:dada/pages/todo_together/detail/todo_together_detail_send_msg.dart';
import 'package:dada/pages/todo_together/detail/todo_together_detail_time.dart';
import 'package:dada/pages/todo_together/detail/todo_together_start.dart';
import 'package:dada/pages/todo_together/detail/todo_together_upload_image.dart';
import 'package:dada/routers/router.dart';
import 'package:dada/utils/image_utils.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

class TodoTogetherDetailPage extends StatefulWidget {
  const TodoTogetherDetailPage({super.key});

  @override
  State<TodoTogetherDetailPage> createState() => _TodoTogetherDetailPageState();
}

class _TodoTogetherDetailPageState extends State<TodoTogetherDetailPage> {
  /// type 1: 开始 2: 自己上传图片 3: 查看对方图片 4: 时间及回应 5: 语音消息
  int type = 1;

  final controller = Get.put(TodoTogetherDetailController());

  String bacImg = Assets.imagesTodoTogetherDetailHomeBac;
  String? bottomLeftImg;
  String? bottomCenterImg;
  String? bottomRightImg;
  String? bottomLeftDes;
  String? bottomCenterDes;
  String? bottomRightDes;

  @override
  void initState() {
    super.initState();
    type = Get.arguments["type"] ?? 1;
    switch (type) {
      case 1:
        bacImg = Assets.imagesTodoTogetherDetailHomeBac;
        bottomLeftImg = Assets.imagesTodoTogetherDetailSuanle;
        bottomRightImg = Assets.imagesTodoTogetherDetailStart;
        break;
      case 2:
      case 3:
        bacImg = Assets.imagesTodoTogetherDetailHomeBac;
        bottomLeftImg = Assets.imagesTodoTogetherDetailSkip;
        bottomRightImg = Assets.imagesTodoTogetherDetailOkBtn;
        break;
      case 4:
        bacImg = Assets.imagesTodoTogetherDetailTimeBac;
        bottomLeftImg = Assets.imagesTodoTogetherDetailChuo;
        bottomRightImg = Assets.imagesTodoTogetherDetailOkBtn;
        bottomLeftDes = "(咦？你还在吗)";
        bottomRightDes = "(我感觉做的差不多了～开始下一步)";
        break;
      case 5:
        bacImg = Assets.imagesTodoTogetherDetailHomeBac;
        bottomCenterImg = Assets.imagesTodoTogetherPublish;
        break;
      case 6:
        bacImg = Assets.imagesTodoTogetherDetailHomeBac;
        bottomLeftImg = Assets.imagesTodoTogetherDetailStopBtn;
        bottomCenterImg = Assets.imagesTodoTogetherDetailAddFriendBtn;
        bottomRightImg = Assets.imagesTodoTogetherDetailAgainBtn;
        bottomLeftDes = "相遇很开心，挥手说再见，下次再会！";
        bottomCenterDes = "就是ta了，结识ta！";
        bottomRightDes = "同一个人继续发起新事";
        break;
      default:
    }
  }

  @override
  Widget build(BuildContext context) {
    return PopScope(
        canPop: false,
        onPopInvokedWithResult: (didPop, result) {
          if (!didPop) {
            _toBack();
          }
        },
        child: Scaffold(
            body: Stack(
          children: [
            ImageUtils.getImage(
                bacImg, ScreenUtil().screenWidth, ScreenUtil().screenHeight,
                fit: BoxFit.fitWidth),
            Positioned.fill(
              child: Column(
                children: [
                  SizedBox(height: ScreenUtil().statusBarHeight),
                  _naviBuilder(),
                  Container(
                    margin: EdgeInsets.only(right: 16.w),
                    width: ScreenUtil().screenWidth,
                    alignment: Alignment.centerRight,
                    child: GestureDetector(
                      onTap: () {},
                      child: Container(
                        child: ImageUtils.getImage(
                            Assets.imagesTodoTogetherDetailMsg, 84.w, 28.h),
                      ),
                    ),
                  ),
                  Visibility(
                      visible: type == 1, child: const TodoTogetherStart()),
                  Visibility(
                      visible: type == 2,
                      child: const TodoTogetherUploadImage()),
                  Visibility(
                      visible: type == 3,
                      child: const TodoTogetherDetailOtherImg()),
                  Visibility(
                      visible: type == 4,
                      child: const TodoTogetherDetailTime()),
                  Visibility(
                      visible: type == 5,
                      child: const TodoTogetherDetailSendMsg()),
                  Visibility(
                      visible: type == 6,
                      child: const TodoTogetherDetailSendMsg(isSend: false)),
                  SizedBox(height: 77.h),
                  _bottomBuilder(),
                  Visibility(
                      visible: type == 4,
                      child: const TodoTogetherDetailTimeAnswer()),
                ],
              ),
            )
          ],
        )));
  }

  Widget _naviBuilder() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        IconButton(
            onPressed: () => _toBack(),
            icon: Image(
                image: const AssetImage(Assets.imagesTodoTogetherDetailBack),
                width: 12.w,
                height: 20.h)),
        Text(
          '一起要做的事',
          style: TextStyles.medium(18.sp, c: Colors.white),
        ),
        TextButton(
            onPressed: () {
              Get.until((router) => Get.currentRoute == GetRouter.todoTogether);
            },
            child: Text(
              '终止',
              style: TextStyles.common(16.sp, Colors.white.withOpacity(0.8)),
            )),
      ],
    );
  }

  Widget _bottomBuilder() {
    // 创建按钮列表
    List<Widget> buttons = [];

    // 左侧按钮
    if (bottomLeftImg != null) {
      buttons.add(
        Column(
          children: [
            if (bottomLeftDes != null)
              Text(
                bottomLeftDes!,
                style: TextStyles.common(12.sp, Colors.white, h: 17.sp / 12.sp),
              ),
            if (bottomLeftDes != null) SizedBox(height: 5.h),
            GestureDetector(
              onTap: () {
                switch (type) {
                  case 1:
                    _toBack();
                    break;
                  case 2:
                    _toNext();
                    break;
                  default:
                }
              },
              child: Image.asset(bottomLeftImg!, width: 105.w),
            ),
          ],
        ),
      );
    }

    // 中间按钮
    if (bottomCenterImg != null) {
      buttons.add(
        Column(
          children: [
            if (bottomCenterDes != null)
              Text(
                bottomCenterDes!,
                textAlign: TextAlign.center,
                style: TextStyles.common(12.sp, Colors.white, h: 17.sp / 12.sp),
              ),
            if (bottomCenterDes != null) SizedBox(height: 5.h),
            GestureDetector(
              onTap: () {
                _toNext();
              },
              child: Image.asset(bottomCenterImg!, width: 105.w),
            ),
          ],
        ),
      );
    }

    // 右侧按钮
    if (bottomRightImg != null) {
      buttons.add(
        Column(
          children: [
            if (bottomRightDes != null)
              Text(
                bottomRightDes!,
                maxLines: 2,
                textAlign: TextAlign.center,
                style: TextStyles.common(12.sp, Colors.white, h: 17.sp / 12.sp),
              ),
            if (bottomRightDes != null) SizedBox(height: 5.h),
            GestureDetector(
              onTap: () {
                _toNext();
              },
              child: Image.asset(bottomRightImg!, width: 105.w),
            ),
          ],
        ),
      );
    }

    // 根据按钮数量决定布局
    return Container(
      width: ScreenUtil().screenWidth,
      padding: EdgeInsets.symmetric(horizontal: 58.w),
      child: Row(
        mainAxisAlignment: buttons.length == 1
            ? MainAxisAlignment.center
            : MainAxisAlignment.spaceBetween,
        crossAxisAlignment: CrossAxisAlignment.end,
        children: buttons.isEmpty
            ? []
            : buttons.length == 1
                ? buttons
                : buttons.length == 2
                    ? [
                        buttons[0],
                        Expanded(child: Center(child: buttons[1])),
                      ]
                    : [
                        buttons[0],
                        Expanded(child: Center(child: buttons[1])),
                        buttons[2],
                      ],
      ),
    );
  }

  void _toNext() {
    type = type + 1;
    Get.toNamed(GetRouter.todoTogetherDetail,
            arguments: {"type": type}, preventDuplicates: false)
        ?.then((v) {
      type = type - 1;
    });
  }

  void _toBack() {
    Get.back();
  }
}
