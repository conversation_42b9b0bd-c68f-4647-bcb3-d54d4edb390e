import 'package:dada/common/values/enums.dart';
import 'package:dada/common/values/text_styles.dart';
import 'package:dada/components/widgets/avatar_widget.dart';
import 'package:dada/generated/assets.dart';
import 'package:dada/model/todo_together_detail_entity.dart';
import 'package:dada/pages/todo_together/detail/bottom_buttons.dart';
import 'package:dada/pages/todo_together/detail/bottom_tips.dart';
import 'package:dada/pages/todo_together/detail/todo_together_detail_controller.dart';
import 'package:dada/pages/todo_together/dialog/todo_together_rich_text.dart';
import 'package:dada/services/network/api_service.dart';
import 'package:dada/services/user/user_service.dart';
import 'package:dada/utils/toast_utils.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:scroll_to_index/scroll_to_index.dart';
import 'package:showcaseview/showcaseview.dart';

class TodoTogetherStart extends StatelessWidget {
  const TodoTogetherStart({super.key});

  @override
  Widget build(BuildContext context) {
    final TodoTogetherDetailController controller =
        Get.find<TodoTogetherDetailController>();
    final TodoTogetherDetailStage1? model = controller.detail?.stage1;
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        SizedBox(height: 16.h),
        const TodoTogetherRichText(
            content: '旅程即将开始!~请确认对方的信息~然后你可以选择是否和TA开始做这件事',
            tipContent:
                '世界很大,能让两个人交会在一起,就是缘分,在这里,你们可以一起开始做同一件事了哦~\n为了让双方都能沉下心来做自己想做的事,在最初的时光里,双方是不可以交流的哦~\n在做完要做的事后,会开放一次双方对一起做这件事的交流机会,也是一次了解对方的机会哈~\n首先,请双方确认对方的信息,选择是否开始一起做件事'),
        SizedBox(height: 20.h),
        Container(
          margin: EdgeInsets.symmetric(horizontal: 24.w),
          height: 44.h,
          padding: EdgeInsets.symmetric(horizontal: 8.w),
          alignment: Alignment.center,
          // padding: EdgeInsets.symmetric(vertical: 11.h),
          decoration: BoxDecoration(
            color: Colors.white.withOpacity(0.34),
            borderRadius: BorderRadius.circular(8.r),
          ),
          child: Text(
            '${controller.isLook ? controller.model?.content : controller.detail?.content}',
            style: TextStyles.medium(16.sp, c: Colors.white)
                .copyWith(overflow: TextOverflow.ellipsis),
          ),
        ),
        SizedBox(height: 32.h),
        if (controller.hasShow) _otherWidget(model, controller),
        if (!controller.hasShow)
          Showcase(
              key: controller.one,
              description:
                  "世界很大，能让两个人交会在一起，就是缘分。在这里，你们可以一起开始做同一件事了哦。\n为了让双方都能沉下心来做自己想做的事，在最初的时光里，双方是不可以交流的哦。\n在做完要做的事后，会开放一次双方对一起做这件事的交流机会，也是一次了解对方的机会哈~\n首先，请双方确认对方的信息，选择是否开始一起做件事。",
              child: _otherWidget(model, controller)),
        SizedBox(height: 24.h),
        Container(
          width: ScreenUtil().screenWidth,
          alignment: Alignment.centerRight,
          child: Container(
            padding: EdgeInsets.symmetric(horizontal: 16.w),
            margin: EdgeInsets.only(right: 24.w),
            height: 84.h,
            constraints: BoxConstraints(minWidth: 0, maxWidth: 300.w),
            decoration: BoxDecoration(
              color: Colors.white.withOpacity(0.28),
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(12.r),
                topRight: Radius.circular(12.r),
                bottomLeft: Radius.circular(12.r),
              ),
              border: Border.all(color: Colors.white, width: 1),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                Column(
                  crossAxisAlignment: CrossAxisAlignment.end,
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text(
                      '我',
                      style: TextStyles.medium(16.sp, c: Colors.white),
                    ),
                    SizedBox(height: 8.h),
                    Row(
                      children: [
                        Image.asset(
                            controller.isLook
                                ? UserService().user?.sex == 0
                                    ? Assets.imagesTodoTogetherMale
                                    : Assets.imagesTodoTogetherFemale
                                : model?.mySex == 0
                                    ? Assets.imagesTodoTogetherMale
                                    : Assets.imagesTodoTogetherFemale,
                            width: 9.w,
                            height: 13.h),
                        SizedBox(width: 5.w),
                        Text(
                          '${controller.isLook ? UserService().user?.age ?? 0 : model?.myAge}岁',
                          style: TextStyles.common(12.sp, Colors.white),
                        ),
                      ],
                    ),
                  ],
                ),
                SizedBox(width: 12.w),
                AvatarWidget(
                  size: 64.h,
                  url: controller.isLook
                      ? (UserService().user?.sex == 0
                          ? Assets.imagesTodoTogetherDetailStartMale
                          : Assets.imagesTodoTogetherDetailStartFemale)
                      : (model?.mySex == 0
                          ? Assets.imagesTodoTogetherDetailStartMale
                          : Assets.imagesTodoTogetherDetailStartFemale),
                ),
              ],
            ),
          ),
        ),
        BottomTips(
            content: "已确认,请等待对方一下吧~",
            visible: model?.myConfirmState == true &&
                model?.otherConfirmState == false &&
                (controller.detail?.stage == null ||
                    controller.detail?.stage == 1)),
        if (controller.detail?.beforeTaskId == null &&
            controller.fromType == TodoTogetherListType.all)
          BottomButtons(
            visible: model?.myConfirmState == false || controller.isLook,
            leftImg: Assets.imagesTodoTogetherDetailSuanle,
            rightImg: Assets.imagesTodoTogetherDetailStart,
            leftOnTap: () {
              if (controller.isLook) {
                Get.back();
              } else {
                controller.showStopDialog(context, onRightBtnTap: () {
                  controller.stop();
                });
              }
            },
            rightOnTap: () async {
              if (controller.isLook) {
                bool? success = await ApiService().todoTogetherJoin(
                    userTaskId: controller.model!.userTaskId!);
                if (!success) {
                  return;
                }
                controller.isLook = false;
              }
              controller.start().then((v) {
                if (v) {
                  controller.getDetailData();
                }
              });
            },
          ),
        if (!(controller.detail?.stage == null ||
            controller.detail?.stage == 1))
          SizedBox(height: 77.h),
      ],
    );
  }

  Widget _otherWidget(TodoTogetherDetailStage1? model,
      TodoTogetherDetailController controller) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 16.w),
      margin: EdgeInsets.only(left: 24.w),
      height: 84.h,
      constraints: BoxConstraints(minWidth: 0, maxWidth: 300.w),
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.28),
        borderRadius: BorderRadius.only(
          topRight: Radius.circular(12.r),
          bottomLeft: Radius.circular(12.r),
          bottomRight: Radius.circular(12.r),
        ),
        border: Border.all(color: Colors.white, width: 1),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          AvatarWidget(
              size: 64.h,
              url: controller.isLook
                  ? (controller.model?.taskSex == 0
                      ? Assets.imagesTodoTogetherDetailStartMale
                      : Assets.imagesTodoTogetherDetailStartFemale)
                  : (model?.otherSex == 0
                      ? Assets.imagesTodoTogetherDetailStartMale
                      : Assets.imagesTodoTogetherDetailStartFemale)),
          SizedBox(width: 12.w),
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Text(
                '对方(未揭晓)',
                style: TextStyles.medium(16.sp, c: Colors.white),
              ),
              SizedBox(height: 8.h),
              Row(
                children: [
                  Text(
                    '${controller.isLook ? controller.model?.taskAge ?? 0 : model?.otherAge}岁',
                    style: TextStyles.common(12.sp, Colors.white),
                  ),
                  SizedBox(width: 5.w),
                  Image.asset(
                      controller.isLook
                          ? controller.model?.taskSex == 0
                              ? Assets.imagesTodoTogetherMale
                              : Assets.imagesTodoTogetherFemale
                          : model?.otherSex == 0
                              ? Assets.imagesTodoTogetherMale
                              : Assets.imagesTodoTogetherFemale,
                      width: 9.w,
                      height: 13.h),
                ],
              ),
            ],
          )
        ],
      ),
    );
  }
}
