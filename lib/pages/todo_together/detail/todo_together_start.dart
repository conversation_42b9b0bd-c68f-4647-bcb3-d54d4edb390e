import 'package:dada/common/values/text_styles.dart';
import 'package:dada/components/widgets/avatar_widget.dart';
import 'package:dada/generated/assets.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class TodoTogetherStart extends StatelessWidget {
  const TodoTogetherStart({super.key});

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        SizedBox(height: 16.h),
        Padding(
          padding: EdgeInsets.symmetric(horizontal: 34.w),
          child: Text(
            '旅程即将开始！～请确认对方的信息～然后你可以选择是否和TA开始做这件事',
            textAlign: TextAlign.center,
            style: TextStyles.common(13.sp, Colors.white, h: 18.sp / 13.sp),
          ),
        ),
        SizedBox(height: 20.h),
        Container(
          margin: EdgeInsets.symmetric(horizontal: 24.w),
          height: 44.h,
          padding: EdgeInsets.symmetric(horizontal: 8.w),
          alignment: Alignment.center,
          // padding: EdgeInsets.symmetric(vertical: 11.h),
          decoration: BoxDecoration(
            color: Colors.white.withOpacity(0.34),
            borderRadius: BorderRadius.circular(8.r),
          ),
          child: Text(
            '一起吃个苹果一起吃个苹果一起吃个苹果一起吃个苹果一起吃个苹果',
            style: TextStyles.medium(16.sp, c: Colors.white)
                .copyWith(overflow: TextOverflow.ellipsis),
          ),
        ),
        SizedBox(height: 32.h),
        Container(
          padding: EdgeInsets.symmetric(horizontal: 16.w),
          margin: EdgeInsets.only(left: 24.w),
          width: 200.w,
          height: 84.h,
          decoration: BoxDecoration(
            color: Colors.white.withOpacity(0.28),
            borderRadius: BorderRadius.only(
              topRight: Radius.circular(12.r),
              bottomLeft: Radius.circular(12.r),
              bottomRight: Radius.circular(12.r),
            ),
            border: Border.all(color: Colors.white, width: 1),
          ),
          child: Row(
            children: [
              AvatarWidget(
                size: 64.h,
                url:
                    'https://dada6.oss-cn-beijing.aliyuncs.com/17416650520631000009651.jpg',
              ),
              SizedBox(width: 12.w),
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Row(
                    children: [
                      Text(
                        '小仙女',
                        style: TextStyles.medium(16.sp, c: Colors.white),
                      ),
                      SizedBox(width: 5.w),
                      Image.asset(Assets.imagesTodoTogetherFemale,
                          width: 9.w, height: 13.h),
                    ],
                  ),
                  SizedBox(height: 8.h),
                  Text(
                    '24岁',
                    style: TextStyles.common(12.sp, Colors.white),
                  ),
                ],
              )
            ],
          ),
        ),
        SizedBox(height: 24.h),
        Container(
          width: ScreenUtil().screenWidth,
          alignment: Alignment.centerRight,
          child: Container(
            padding: EdgeInsets.symmetric(horizontal: 16.w),
            margin: EdgeInsets.only(right: 24.w),
            width: 200.w,
            height: 84.h,
            decoration: BoxDecoration(
              color: Colors.white.withOpacity(0.28),
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(12.r),
                topRight: Radius.circular(12.r),
                bottomLeft: Radius.circular(12.r),
              ),
              border: Border.all(color: Colors.white, width: 1),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                Column(
                  crossAxisAlignment: CrossAxisAlignment.end,
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Row(
                      children: [
                        Image.asset(Assets.imagesTodoTogetherFemale,
                            width: 9.w, height: 13.h),
                        SizedBox(width: 5.w),
                        Text(
                          '小仙女',
                          style: TextStyles.medium(16.sp, c: Colors.white),
                        ),
                      ],
                    ),
                    SizedBox(height: 8.h),
                    Text(
                      '24岁',
                      style: TextStyles.common(12.sp, Colors.white),
                    ),
                  ],
                ),
                SizedBox(width: 12.w),
                AvatarWidget(
                  size: 64.h,
                  url:
                      'https://dada6.oss-cn-beijing.aliyuncs.com/17416650520631000009651.jpg',
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }
}
