import 'package:dada/common/values/text_styles.dart';
import 'package:dada/generated/assets.dart';
import 'package:dada/utils/image_utils.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class TodoTogetherDetailOtherImg extends StatelessWidget {
  final bool hasImg;
  const TodoTogetherDetailOtherImg({super.key, this.hasImg = true});

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        SizedBox(height: 39.h),
        Padding(
          padding: EdgeInsets.symmetric(horizontal: 34.w),
          child: Text(
            '起航!你们要开始一起XXX了哦，作为开始做这件事的证明，你们可以各自上传一张准备做这件事的图片哦~',
            textAlign: TextAlign.center,
            style: TextStyles.common(13.sp, Colors.white, h: 18.sp / 13.sp),
          ),
        ),
        SizedBox(height: 64.h),
        !hasImg
            ? Container(
                width: 200,
                height: 200,
                decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(8.r),
                    border:
                        Border.all(color: const Color(0xFFFBFBFB), width: 1)),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    ImageUtils.getImage(
                        Assets.imagesTodoTogetherDetailNoPicture, 111, 96),
                    SizedBox(height: 17.h),
                    Text(
                      '我准备好做这件事啦~',
                      style: TextStyles.common(14.sp, Colors.white),
                    )
                  ],
                ))
            : Row(
                crossAxisAlignment: CrossAxisAlignment.end,
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Container(
                    width: 200,
                    height: 200,
                    decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(8.r),
                        border: Border.all(
                            color: const Color(0xFFFBFBFB), width: 1)),
                    child: ImageUtils.getImage(
                        Assets.imagesTodoTogetherDetailNoPicture, 200, 200),
                  ),
                  SizedBox(width: 14.w),
                  Container(
                      padding: EdgeInsets.only(bottom: 3.h),
                      decoration: BoxDecoration(
                        border: Border(
                            bottom: BorderSide(color: Colors.white, width: 1)),
                      ),
                      child: Text('举报',
                          style: TextStyles.common(14.sp, Colors.white))),
                ],
              )
      ],
    );
  }
}
