import 'dart:async';

import 'package:dada/common/values/enums.dart';
import 'package:dada/model/todo_together_detail_entity.dart';
import 'package:dada/model/todo_together_list_entity.dart';
import 'package:dada/pages/todo_together/dialog/todo_together_dialog.dart';
import 'package:dada/pages/todo_together/dialog/todo_together_stop_dialog.dart';
import 'package:dada/pages/todo_together/list/todo_together_list_controller.dart';
import 'package:dada/routers/router.dart';
import 'package:dada/services/event_bus/event_bus_engine.dart';
import 'package:dada/services/network/api_service.dart';
import 'package:dada/utils/toast_utils.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:scroll_to_index/scroll_to_index.dart';
import 'package:showcaseview/showcaseview.dart';
import 'package:sp_util/sp_util.dart';

class TodoTogetherDetailController extends GetxController {
  RxString uploadImg = "".obs;
  RxString otherImg = "".obs;
  RxString sendMsgImg = "".obs;
  RxString audioPath = "".obs;

  Rx<TodoTogetherDetailEntity>? detailEntity;

  TodoTogetherDetailEntity? detail;

  AutoScrollController scrollController = AutoScrollController();

  List contents = [];

  String? userTaskId;
  TodoTogetherListType? fromType; //区分全部还是历史进入详情
  bool isLook = false; //是否是点击查看按钮进入 是-不请求详情数据 否-请求详情数据
  TodoTogetherListEntity? model;

  final bool hasShow = SpUtil.getBool('showcase_together_detail') ?? false;
  final GlobalKey one = GlobalKey();

  @override
  void onInit() {
    super.onInit();
    ShowcaseView.register(scope: "together_detail_page");
    userTaskId = Get.arguments["todoId"];
    fromType = Get.arguments["type"];
    isLook = Get.arguments["isLook"] ?? false;
    model = Get.arguments["model"];
    if (!isLook) {
      getDetailData(showLoading: true);
    } else {
      contents.add({});
    }
  }

  @override
  void onClose() {
    super.onClose();
  }

  void getDetailData({bool? showLoading}) async {
    detail = await ApiService().todoTogetherDetail(
        userTaskId: userTaskId, showLoading: showLoading ?? false);
    if (detail == null) {
      Get.back();
      return;
    }

    if ((detail?.stage ?? 1) == 1) {
      if (!hasShow) {
        ShowcaseView.getNamed('together_detail_page')
            .startShowCase([one], delay: const Duration(milliseconds: 200));
        SpUtil.putBool('showcase_together_detail', true);
      }
    }
    if (isLook) {
      isLook = (detail?.stage ?? 1) == 1;
    }
    // 清空contents
    contents.clear();
    switch (detail?.stage ?? 1) {
      case 1:
        contents.add(detail?.stage1);
        break;
      case 2:
        contents.add(detail?.stage1);
        contents.add(detail?.stage2);
        break;
      case 3:
        contents.add(detail?.stage1);
        contents.add(detail?.stage2);
        contents.add(detail?.stage3);
        break;
      case 4:
        contents.add(detail?.stage1);
        contents.add(detail?.stage2);
        contents.add(detail?.stage3);
        contents.add(detail?.stage4);
        break;
      case 5:
        contents.add(detail?.stage1);
        contents.add(detail?.stage2);
        contents.add(detail?.stage3);
        contents.add(detail?.stage4);
        contents.add(detail?.stage5);
        break;
      case 6:
      case 7:
        contents.add(detail?.stage1);
        contents.add(detail?.stage2);
        contents.add(detail?.stage3);
        contents.add(detail?.stage4);
        contents.add(detail?.stage5);
        contents.add(detail?.stage6);
        if (detail?.stage6?.isMyEndSelect != null &&
            detail?.stage6?.isOtherEndSelect != null) {
          contents.add({
            "mySelect": detail?.stage6?.isMyEndSelect,
            "otherSelect": detail?.stage6?.isOtherEndSelect
          });
        }

        break;
      case 8:
        contents.add(detail?.stage1);
        contents.add(detail?.stage2);
        contents.add(detail?.stage3);
        contents.add(detail?.stage4);
        contents.add(detail?.stage5);
        contents.add(detail?.stage6);
        contents.add({
          "mySelect": detail?.stage6?.isMyEndSelect,
          "otherSelect": detail?.stage6?.isOtherEndSelect
        });
        contents.add(detail?.stage8);
        break;
      default:
    }

    //终止按钮显示逻辑
    if (detail?.stage == 6 || detail?.stage == 7) {}

    update();
    Future.delayed(const Duration(milliseconds: 100)).then((value) {
      scrollController.scrollToIndex(contents.length - 1,
          preferPosition: AutoScrollPosition.begin);
    });
  }

  Future<bool> start() async {
    bool? res = await ApiService().todoTogetherStart(userTaskId: userTaskId);
    getDetailData();
    return res;
  }

  ///上传自己图片
  void uploadImgMyself() async {
    String? fileUrl = await ApiService().uploadFile(uploadImg.value);
    if (fileUrl != null && fileUrl.isNotEmpty) {
      await ApiService()
          .todoTogetherUploadImg(userTaskId: userTaskId, imgurl: fileUrl);
      getDetailData();
    }
  }

  void skip() async {
    await ApiService().todoTogetherSkip(userTaskId: userTaskId);
    getDetailData();
  }

  void getOtherImg() async {
    Map? res = await ApiService().todoTogetherOtherImg(userTaskId: userTaskId);
    if (res != null) {
      otherImg.value = res['msg'];
    }
  }

  void continueOtherImg() async {
    await ApiService().todoTogetherStartImgContinue(userTaskId: userTaskId);
    getDetailData();
  }

  void poke() async {
    if (detail?.stage4?.isChuoState == true) {
      ToastUtils.showToast('一天只能戳一次哦~');
      return;
    }
    bool? res = await ApiService().todoTogetherPoke(userTaskId: userTaskId);
    if (res != null && res) {
      getDetailData();
    }
  }

  void respond() async {
    bool? res = await ApiService().todoTogetherRespond(userTaskId: userTaskId);
    if (res != null && res) {
      getDetailData();
    }
  }

  void complete() async {
    await ApiService().todoTogetherComplete(userTaskId: userTaskId);
    getDetailData();
  }

  void completeToMsg(String? msg) async {
    String? imageUrl;
    String? audioUrl;
    if (sendMsgImg.value.isNotEmpty) {
      imageUrl = await ApiService().uploadFile(sendMsgImg.value);
    }
    if (audioPath.value.isNotEmpty) {
      audioUrl = await ApiService().uploadFile(audioPath.value);
    }
    bool? res = await ApiService().todoTogetherCompleteToMsg(userTaskId!,
        imageUrl: imageUrl, audioUrl: audioUrl, msg: msg);
    if (res != null && !res) {}
    getDetailData();
  }

  void endSelect(String? select) async {
    await ApiService()
        .todoTogetherEndSelect(userTaskId: userTaskId, endSelect: select);
    getDetailData();
  }

  void propose(String? msg) async {
    if (msg == null || msg.isEmpty) {
      ToastUtils.showToast('请输入文字后提交...');
      return;
    }
    await ApiService().todoTogetherPropose(userTaskId, msg);
    getDetailData();
  }

  void proposeSelect(String propose) async {
    String? res = await ApiService().todoTogetherProposeSelect(
        userTaskId: userTaskId, proposeSelect: propose);
    if (res != null && res.isNotEmpty) {
      end();
      Get.offNamed(GetRouter.todoTogetherDetail,
          preventDuplicates: false,
          arguments: {"todoId": res, "type": TodoTogetherListType.all});
    }
  }

  void end() {
    if (Get.find<TodoTogetherListController>(
                tag: 'TodoTogetherListController_${fromType.toString()}')
            .currentEntity
            .state !=
        4) {
      ApiService().todoTogetherEnd(userTaskId);
    }
  }

  void stop() async {
    bool? res = await ApiService().todoTogetherStop(userTaskId: userTaskId);
    if (res) {
      Get.until((router) => Get.currentRoute == GetRouter.todoTogether);
    }
  }

  void showStopDialog(BuildContext c,
      {void Function()? onRightBtnTap, String? content, String? rightText}) {
    showDialog(
        context: c,
        builder: (_) {
          return TodoTogetherDialog(
            content: content ?? '此操作将终止事件哦,确定这么做吗?',
            onRightBtnTap: onRightBtnTap,
            onLeftBtnTap: () {
              Get.back();
            },
            rightBtnTitle: rightText ?? '终止',
            leftBtnTitle: '取消',
          );
        });
  }
}
