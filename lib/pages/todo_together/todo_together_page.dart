import 'package:dada/common/values/colors.dart';
import 'package:dada/common/values/enums.dart';
import 'package:dada/common/values/text_styles.dart';
import 'package:dada/components/widgets/custom_text_field.dart';
import 'package:dada/generated/assets.dart';
import 'package:dada/pages/home/<USER>';
import 'package:dada/pages/todo_together/list/todo_together_list_controller.dart';
import 'package:dada/pages/todo_together/list/todo_together_list_page.dart';
import 'package:dada/pages/todo_together/todo_together_controller.dart';
import 'package:dada/routers/router.dart';
import 'package:dada/utils/image_utils.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:showcaseview/showcaseview.dart';
import 'package:sp_util/sp_util.dart';

class TodoTogetherPage extends StatefulWidget {
  const TodoTogetherPage({super.key});

  @override
  State<TodoTogetherPage> createState() => _TodoTogetherPageState();
}

class _TodoTogetherPageState extends State<TodoTogetherPage> {
  TextEditingController textEditingController = TextEditingController();
  final controller = Get.put(TodoTogetherController());
  final GlobalKey _one = GlobalKey();
  bool hasShow = SpUtil.getBool("showcase_together") ?? false;

  @override
  void initState() {
    super.initState();

    if (!hasShow) {
      ShowcaseView.register(scope: "todo_together_page");
      WidgetsBinding.instance.addPostFrameCallback((_) {
        ShowcaseView.getNamed("todo_together_page").startShowCase(
          [_one],
          delay: const Duration(milliseconds: 200),
        );
        SpUtil.putBool("showcase_together", true);
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return PopScope(
        canPop: false,
        onPopInvokedWithResult: (didPop, result) {
          Get.find<HomeController>().todoTogetherUnreadId.value = "";
          if (!didPop) {
            Get.back();
          }
        },
        child: Scaffold(
          body: Container(
            decoration: const BoxDecoration(
                image: DecorationImage(
                    image: AssetImage(Assets.imagesTodoTogetherBac),
                    fit: BoxFit.cover)),
            child: Column(
              children: [
                SizedBox(height: ScreenUtil().statusBarHeight),
                _buildHeaderView(),
                ImageUtils.getImage(
                    Assets.imagesTodoTogetherBanner, 328.w, 80.h,
                    fit: BoxFit.fitWidth),
                SizedBox(height: 18.h - 7.w),
                _buildCenterView(),
                _buildListView(),
              ],
            ),
          ),
        ));
  }

  Widget _buildHeaderView() {
    return Row(
      children: [
        IconButton(
            onPressed: () {
              Get.back();
            },
            icon: Image(
                image: AssetImage(Assets.imagesNaviBackWhite),
                width: 12.w,
                height: 20.h)),
        Expanded(
          child: Container(
            height: 35.h,
            decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(19.r),
                color: AppColors.colorFFBABABA.withOpacity(0.17),
                border: Border.all(color: Colors.white, width: 1)),
            child: Row(
              children: [
                Padding(
                  padding: EdgeInsets.only(left: 9.w),
                  child: ImageUtils.getImage(
                      Assets.imagesTodoTogetherSearch, 15, 15),
                ),
                SizedBox(
                  width: 240.w,
                  height: 35.h,
                  child: CustomTextField.build(
                    contentPadding: EdgeInsets.only(left: 7.w),
                    controller: textEditingController,
                    hintText: "搜索房间",
                    hintStyle: TextStyles.common(12.sp, Colors.white),
                    showSuffixIcon: true,
                    focusNode: controller.focusNode,
                    textInputAction: TextInputAction.search,
                    suffixIconOnTap: () {
                      controller.update();
                    },
                    style: TextStyles.common(13.sp, AppColors.colorFF333333),
                    onChanged: (value) {
                      controller.searchKey.value = value;
                    },
                    onSubmitted: (value) {
                      // controller.listRefreshData(TodoTogetherListType.all);
                    },
                  ),
                ),
              ],
            ),
          ),
        ),
        IconButton(
            onPressed: () {
              Get.toNamed(GetRouter.todoTogetherHistory)?.then((v) {
                // controller.listRefreshData(TodoTogetherListType.all);
              });
            },
            icon: Image(
                image: AssetImage(Assets.imagesTodoTogetherHistoryBtnIcon),
                fit: BoxFit.contain,
                width: 38.w,
                height: 36.h)),
      ],
    );
  }

  Widget _buildCenterView() {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 21.w),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          GestureDetector(
            behavior: HitTestBehavior.opaque,
            onTap: () {
              controller.fullRoomHidden.value =
                  !controller.fullRoomHidden.value;
              // controller.listRefreshData(TodoTogetherListType.all);
            },
            child: Row(
              children: [
                Obx(
                  () => Container(
                    width: 16,
                    height: 16,
                    alignment: Alignment.center,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(3.r),
                      border:
                          Border.all(color: AppColors.colorFFA5A9FB, width: 1),
                    ),
                    child: Visibility(
                        visible: controller.fullRoomHidden.value,
                        child: ImageUtils.getImage(
                            Assets.imagesTodoTogetherCheck, 13, 13)),
                  ),
                ),
                SizedBox(width: 7.w),
                Text(
                  '显示已满房间',
                  style: TextStyles.common(14.sp, AppColors.colorFF686868),
                )
              ],
            ),
          ),
          if (hasShow)
            GestureDetector(
              onTap: () {
                Get.toNamed(GetRouter.todoTogetherPublish)?.then((v) {
                  // controller.listRefreshData(TodoTogetherListType.all);
                });
              },
              child: Image.asset(Assets.imagesTodoTogetherPublish,
                  width: 82.w, fit: BoxFit.fitWidth),
            ),
          if (!hasShow)
            Showcase(
              key: _one,
              description:
                  "你可以把这里理解为一个小伙伴结识、筛选器哦。\n不用表露身份，不用费心聊天，安心做想做的任何事！\n做完后，才会获得仅有的一次交流机会哦，通过这一次交流，考察对方的性情，只有你认可了对方才会有后续哦~不喜欢的绝无纠缠。\n很适合结识认真走心还契合你喜好的伙伴哦！放心发布或加入吧~\n今天的随意之举，一不小心就给你带来美好哦！",
              child: GestureDetector(
                onTap: () {
                  Get.toNamed(GetRouter.todoTogetherPublish)?.then((v) {
                    // controller.listRefreshData(TodoTogetherListType.all);
                  });
                },
                child: Image.asset(Assets.imagesTodoTogetherPublish,
                    width: 82.w, fit: BoxFit.fitWidth),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildListView() {
    return Expanded(child: TodoTogetherListPage());
  }
}
