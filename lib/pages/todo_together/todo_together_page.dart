import 'package:dada/common/values/colors.dart';
import 'package:dada/common/values/enums.dart';
import 'package:dada/common/values/text_styles.dart';
import 'package:dada/components/widgets/custom_text_field.dart';
import 'package:dada/generated/assets.dart';
import 'package:dada/pages/todo_together/list/todo_together_list_controller.dart';
import 'package:dada/pages/todo_together/list/todo_together_list_page.dart';
import 'package:dada/pages/todo_together/todo_together_controller.dart';
import 'package:dada/routers/router.dart';
import 'package:dada/utils/image_utils.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

class TodoTogetherPage extends StatefulWidget {
  const TodoTogetherPage({super.key});

  @override
  State<TodoTogetherPage> createState() => _TodoTogetherPageState();
}

class _TodoTogetherPageState extends State<TodoTogetherPage> {
  TextEditingController textEditingController = TextEditingController();
  final controller = Get.put(TodoTogetherController());
  final listController =
      Get.put(TodoTogetherListController(type: TodoTogetherListType.all));

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: const BoxDecoration(
            image: DecorationImage(
                image: AssetImage(Assets.imagesTodoTogetherBac),
                fit: BoxFit.cover)),
        child: Column(
          children: [
            SizedBox(height: ScreenUtil().statusBarHeight),
            _buildHeaderView(),
            ImageUtils.getImage(Assets.imagesTodoTogetherBanner, 328.w, 80.h,
                fit: BoxFit.fitWidth),
            SizedBox(height: 18.h - 7.w),
            _buildCenterView(),
            _buildListView(),
          ],
        ),
      ),
    );
  }

  Widget _buildHeaderView() {
    return Row(
      children: [
        IconButton(
            onPressed: () {
              Get.back();
            },
            icon: Image(
                image: AssetImage(Assets.imagesNaviBackWhite),
                width: 12.w,
                height: 20.h)),
        Expanded(
          child: Container(
            height: 35.h,
            decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(19.r),
                color: AppColors.colorFFBABABA.withOpacity(0.17),
                border: Border.all(color: Colors.white, width: 1)),
            child: Row(
              children: [
                Padding(
                  padding: EdgeInsets.only(left: 9.w),
                  child: ImageUtils.getImage(
                      Assets.imagesTodoTogetherSearch, 15, 15),
                ),
                SizedBox(
                  width: 240.w,
                  height: 35.h,
                  child: CustomTextField.build(
                    contentPadding: EdgeInsets.only(left: 7.w),
                    controller: textEditingController,
                    hintText: "搜索房间",
                    hintStyle: TextStyles.common(12.sp, Colors.white),
                    showSuffixIcon: true,
                    focusNode: controller.focusNode,
                    textInputAction: TextInputAction.search,
                    suffixIconOnTap: () {
                      controller.update();
                    },
                    style: TextStyles.common(13.sp, AppColors.colorFF333333),
                    onChanged: (value) {
                      controller.searchKey.value = value;
                    },
                    onSubmitted: (value) {},
                  ),
                ),
              ],
            ),
          ),
        ),
        IconButton(
            onPressed: () {
              Get.toNamed(GetRouter.todoTogetherHistory);
            },
            icon: Image(
                image: AssetImage(Assets.imagesTodoTogetherHistoryBtnIcon),
                fit: BoxFit.contain,
                width: 38.w,
                height: 36.h)),
      ],
    );
  }

  Widget _buildCenterView() {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 21.w),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          GestureDetector(
            behavior: HitTestBehavior.opaque,
            onTap: () {
              controller.fullRoomHidden.value =
                  !controller.fullRoomHidden.value;
            },
            child: Row(
              children: [
                Obx(
                  () => Container(
                    width: 16,
                    height: 16,
                    alignment: Alignment.center,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(3.r),
                      border:
                          Border.all(color: AppColors.colorFFA5A9FB, width: 1),
                    ),
                    child: Visibility(
                        visible: !controller.fullRoomHidden.value,
                        child: ImageUtils.getImage(
                            Assets.imagesTodoTogetherCheck, 13, 13)),
                  ),
                ),
                SizedBox(width: 7.w),
                Text(
                  '显示已满房间',
                  style: TextStyles.common(14.sp, AppColors.colorFF686868),
                )
              ],
            ),
          ),
          GestureDetector(
            onTap: () {
              Get.toNamed(GetRouter.todoTogetherPublish);
            },
            child: Image.asset(Assets.imagesTodoTogetherPublish,
                width: 82.w, fit: BoxFit.fitWidth),
          ),
        ],
      ),
    );
  }

  Widget _buildListView() {
    return const Expanded(
        child: TodoTogetherListPage(type: TodoTogetherListType.all));
  }
}
