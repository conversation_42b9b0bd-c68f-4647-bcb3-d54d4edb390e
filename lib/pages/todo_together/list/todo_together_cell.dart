import 'package:dada/common/values/colors.dart';
import 'package:dada/common/values/text_styles.dart';
import 'package:dada/generated/assets.dart';
import 'package:dada/model/todo_together_list_entity.dart';
import 'package:dada/pages/todo_together/dialog/todo_together_dialog.dart';
import 'package:dada/routers/router.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

class TodoTogetherCell extends StatelessWidget {
  final TodoTogetherListEntity model;
  const TodoTogetherCell({super.key, required this.model});

  @override
  Widget build(BuildContext context) {
    String bacImg = model.type == 0
        ? Assets.imagesTodoTogetherListBacOther
        : Assets.imagesTodoTogetherListBac;
    String typeImg = model.type == 1
        ? Assets.imagesTodoTogetherListMyCreate
        : Assets.imagesTodoTogetherListMyJoin;
    String status =
        model.state == 1 ? '等待进入...' : (model.state == 2 ? '进行中...' : '已完成');
    String? statusBtnImg;
    if (model.type == 1) {
      if (model.state == 1) {
        statusBtnImg = Assets.imagesTodoTogetherListDelete;
      } else if (model.state == 2) {
        statusBtnImg = Assets.imagesTodoTogetherListContinue;
      } else {
        statusBtnImg = null;
      }
    } else if (model.type == 2) {
      if (model.state == 2) {
        statusBtnImg = Assets.imagesTodoTogetherListContinue;
      } else {
        statusBtnImg = null;
      }
    } else {
      if (model.state == 1) {
        statusBtnImg = Assets.imagesTodoTogetherJoin;
      } else if (model.state == 2) {
        statusBtnImg = Assets.imagesTodoTogetherListContinue;
      } else {
        statusBtnImg = null;
      }
    }

    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 16.w),
      child: GestureDetector(
        onTap: () => _pushToDetail(context),
        child: Container(
          margin: EdgeInsets.only(top: 10.h),
          child: Stack(
            children: [
              Image.asset(bacImg, width: 343.w, fit: BoxFit.fitWidth),
              Visibility(
                visible: model.type != 0,
                child: Positioned(
                    right: 0,
                    top: 3.h,
                    child: Image.asset(
                      typeImg,
                      width: 92.w,
                      fit: BoxFit.fitWidth,
                    )),
              ),
              Padding(
                padding: EdgeInsets.only(left: 12.w),
                child: Row(
                  children: [
                    Container(
                      alignment: Alignment.centerLeft,
                      height: 35.h + 1,
                      child: Text('我想找个人一起',
                          style: TextStyles.medium(14.sp,
                              c: AppColors.colorFF686868)),
                    ),
                    SizedBox(width: 10.w),
                    Image.asset(
                        model.taskSex == 0
                            ? Assets.imagesTodoTogetherMale
                            : Assets.imagesTodoTogetherFemale,
                        width: model.taskSex == 0 ? 10.4.w : 9.w,
                        height: model.taskSex == 0 ? 11.h : 13.h),
                    SizedBox(width: 2.w),
                    Text(
                      model.sex == 2 ? '不限' : '限',
                      style: TextStyles.common(12.sp, AppColors.colorFFA4A3AD),
                    ),
                    SizedBox(width: 2.w),
                    Visibility(
                        visible: model.sex == 0,
                        child: Image.asset(Assets.imagesTodoTogetherMale,
                            width: 10.4.w, height: 11.h)),
                    SizedBox(width: 2.w),
                    Visibility(
                        visible: model.sex == 1,
                        child: Image.asset(Assets.imagesTodoTogetherFemale,
                            width: 9.w, height: 13.h)),
                    SizedBox(width: 2.w),
                    Visibility(
                        visible: model.sex == 2,
                        child: Image.asset(Assets.imagesTodoTogetherMaleFemale,
                            width: 13.w, height: 11.h)),
                    SizedBox(width: 10.w),
                    Text(
                      status,
                      style: TextStyles.common(12.sp, AppColors.colorFFA4A3AD),
                    ),
                  ],
                ),
              ),
              Positioned(
                top: 35.h + 8.h,
                child: Stack(
                  alignment: Alignment.center,
                  children: [
                    Image.asset(Assets.imagesTodoTogetherListBubble,
                        width: 208.w, fit: BoxFit.fitWidth),
                    SizedBox(
                      width: 180.w,
                      child: Text(
                        model.content ?? '',
                        textAlign: TextAlign.center,
                        maxLines: 1,
                        style:
                            TextStyles.medium(14.sp, c: AppColors.colorFF686868)
                                .copyWith(overflow: TextOverflow.ellipsis),
                      ),
                    ),
                  ],
                ),
              ),
              if (statusBtnImg != null)
                Positioned(
                    top: 35.h + 12.h,
                    right: 8.w,
                    child: Row(
                      children: [
                        IconButton(
                            onPressed: () => _pushToDetail(context),
                            icon: Image.asset(
                              statusBtnImg,
                              width: 69.w,
                              fit: BoxFit.fitWidth,
                            ))
                      ],
                    )),
            ],
          ),
        ),
      ),
    );
  }

  void _pushToDetail(BuildContext c) {
    if (model.type == 1 && model.state == 1) {
      showDialog(
          context: c,
          builder: (_) {
            return TodoTogetherDialog(
              content: '确定要删除吗？',
              onRightBtnTap: () {
                Get.back();
              },
              onLeftBtnTap: () {
                Get.back();
              },
              rightBtnTitle: '删除',
              leftBtnTitle: '取消',
            );
          });
    } else {
      Get.toNamed(GetRouter.todoTogetherDetail, arguments: {
        "type": 1,
        "todoId": model.userTaskId.toString(),
      });
    }
  }
}
