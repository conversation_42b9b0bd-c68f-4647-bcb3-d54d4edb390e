import 'package:dada/common/values/enums.dart';
import 'package:dada/components/widgets/list_page_empty_widget.dart';
import 'package:dada/components/widgets/refresh_widget.dart';
import 'package:dada/pages/todo_together/list/todo_together_cell.dart';
import 'package:dada/pages/todo_together/list/todo_together_list_controller.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

class TodoTogetherListPage extends StatefulWidget {
  final TodoTogetherListType type;

  const TodoTogetherListPage({super.key, this.type = TodoTogetherListType.all});

  @override
  State<TodoTogetherListPage> createState() => _TodoTogetherListPageState();
}

class _TodoTogetherListPageState extends State<TodoTogetherListPage> {
  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return GetBuilder<TodoTogetherListController>(
      init: TodoTogetherListController(type: widget.type),
      global: false,
      builder: (controller) {
        return RefreshWidget.build(
            refreshController: controller.refreshController,
            onRefresh: () => controller.refreshData(),
            onLoadMore: () => controller.loadMoreData(),
            child: controller.data.isEmpty
                ? ListPageEmptyWidget(naviBarHeight: 150.h)
                : ListView.builder(
                    padding: EdgeInsets.zero,
                    itemCount: controller.data.length,
                    itemBuilder: (c, i) {
                      final model = controller.data[i];
                      return TodoTogetherCell(
                          model: model, type: controller.type);
                    },
                  ));
      },
      id: TodoTogetherListController(type: widget.type).refreshId,
    );
  }

  @override
  void dispose() {
    super.dispose();
  }
}
