import 'package:dada/common/values/enums.dart';
import 'package:dada/components/widgets/list_page_empty_widget.dart';
import 'package:dada/components/widgets/refresh_widget.dart';
import 'package:dada/pages/todo_together/list/todo_together_cell.dart';
import 'package:dada/pages/todo_together/list/todo_together_list_controller.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class TodoTogetherListPage extends StatefulWidget {
  final TodoTogetherListType type;
  const TodoTogetherListPage({super.key, required this.type});

  @override
  State<TodoTogetherListPage> createState() => _TodoTogetherListPageState();
}

class _TodoTogetherListPageState extends State<TodoTogetherListPage> {
  @override
  Widget build(BuildContext context) {
    return GetBuilder<TodoTogetherListController>(
        init: TodoTogetherListController(type: widget.type),
        global: false,
        id: TodoTogetherListController(type: widget.type).refreshId,
        builder: (controller) {
          return RefreshWidget.build(
              refreshController: controller.refreshController,
              onRefresh: () => controller.refreshData(),
              onLoadMore: () => controller.loadMoreData(),
              child: controller.data.isEmpty
                  ? const ListPageEmptyWidget()
                  : MediaQuery.removePadding(
                      context: context,
                      removeTop: true,
                      child: ListView.builder(
                        itemCount: controller.data.length,
                        itemBuilder: (c, i) {
                          final model = controller.data[i];
                          return TodoTogetherCell(model: model);
                        },
                      )));
        });
  }
}
