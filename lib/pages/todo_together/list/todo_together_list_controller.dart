import 'package:dada/common/values/enums.dart';
import 'package:dada/components/controller/list_page_controller.dart';
import 'package:dada/model/todo_together_list_entity.dart';
import 'package:dada/pages/todo_together/list/todo_together_list_page.dart';
import 'package:dada/pages/todo_together/todo_together_controller.dart';
import 'package:dada/services/network/api_service.dart';
import 'package:get/get.dart';

class TodoTogetherListController extends ListPageController<
    TodoTogetherListEntity, TodoTogetherListController> {
  late TodoTogetherListType type;

  TodoTogetherListController({required this.type});
  @override
  Future<List<TodoTogetherListEntity>?> loadData(int page) async {
    final controller = Get.find<TodoTogetherController>();
    String state = controller.fullRoomHidden.value ? "0" : "1";
    String content = controller.searchKey.value;
    // List<TodoTogetherListEntity>? list = await ApiService().getTodoTogetherList(
    //     pageIndex: pageIndex, state: state, content: content);
    List<Map<String, dynamic>>? listMap = type == TodoTogetherListType.all
        ? [
            {
              "title": "我想找个人一起",
              "sex": 1,
              "taskSex": 0,
              "type": 1,
              "state": 1,
              "content": "吃个苹果"
            },
            {
              "title": "我想找个人一起",
              "sex": 0,
              "taskSex": 1,
              "type": 1,
              "state": 2,
              "content": "一天喝八杯水"
            },
            {
              "title": "我想找个人一起",
              "sex": 2,
              "taskSex": 0,
              "type": 2,
              "state": 3,
              "content": "一起背诵高一语文课本第二页第三端从第10个字背诵到第100个字"
            },
          ]
        : [
            {
              "title": "我想找个人一起",
              "sex": 2,
              "taskSex": 0,
              "type": 2,
              "state": 3,
              "content": "一起背诵高一语文课本第二页第三端从第10个字背诵到第100个字"
            },
          ];
    List<TodoTogetherListEntity>? list =
        listMap.map((e) => TodoTogetherListEntity.fromJson(e)).toList();
    return list;
  }
}
