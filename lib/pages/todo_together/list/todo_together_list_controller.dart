import 'package:dada/common/values/enums.dart';
import 'package:dada/components/controller/list_page_controller.dart';
import 'package:dada/model/todo_together_list_entity.dart';
import 'package:dada/pages/todo_together/todo_together_controller.dart';
import 'package:dada/routers/router.dart';
import 'package:dada/services/network/api_service.dart';
import 'package:get/get.dart';

class TodoTogetherListController extends ListPageController<
    TodoTogetherListEntity, TodoTogetherListController> {
  TodoTogetherListController();

  TodoTogetherListEntity currentEntity = TodoTogetherListEntity();

  final TodoTogetherListType type = TodoTogetherListType.all;

  @override
  Future<List<TodoTogetherListEntity>?> loadData(int page) async {
    List<TodoTogetherListEntity>? list;
    // if (type == TodoTogetherListType.all) {
    final controller = Get.find<TodoTogetherController>();
    String state = controller.fullRoomHidden.value ? "0" : "1";
    String content = controller.searchKey.value;
    list = await ApiService().getTodoTogetherList(
        pageIndex: pageIndex, state: state, content: content);
    /* } else {
      list =
          await ApiService().getTodoTogetherHistoryList(pageIndex: pageIndex);
    } */

    return list;
  }

  Future<bool> join(String userTaskId) async {
    bool? success = await ApiService().todoTogetherJoin(userTaskId: userTaskId);
    return success;
  }

  void delete(String? id) async {
    bool? success =
        await ApiService().todoTogetherDelete(userTaskId: int.parse(id!));
    if (success) {
      data.remove(data.firstWhere((e) => e.userTaskId == id));
      update([refreshId]);
      Get.back();
    } else {}
  }

  void joinToDetail(String? userTaskId) {
    join(userTaskId ?? "").then((v) {
      if (v) {
        Get.toNamed(GetRouter.todoTogetherDetail,
            arguments: {"todoId": userTaskId, "type": type})?.then((v) {
          refreshData();
        });
      }
    });
  }

  void toDetail(String? userTaskId, bool islook, TodoTogetherListEntity model) {
    Get.toNamed(GetRouter.todoTogetherDetail, arguments: {
      "todoId": userTaskId,
      "type": type,
      "isLook": islook,
      "model": model
    })?.then((v) {
      refreshData();
    });
  }
}
