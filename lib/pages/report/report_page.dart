import 'dart:io';

import 'package:dada/common/values/colors.dart';
import 'package:dada/common/values/enums.dart';
import 'package:dada/common/values/text_styles.dart';
import 'package:dada/components/widgets/common_gradient_btn.dart';
import 'package:dada/components/widgets/custom_app_bar.dart';
import 'package:dada/components/widgets/custom_text_field.dart';
import 'package:dada/components/widgets/image_browser.dart';
import 'package:dada/components/widgets/label_item_widget.dart';
import 'package:dada/components/widgets/permission_use_desc_dialog.dart';
import 'package:dada/configs/app_config.dart';
import 'package:dada/generated/assets.dart';
import 'package:dada/generated/l10n.dart';
import 'package:dada/services/network/api_service.dart';
import 'package:dada/utils/image_picker_utils.dart';
import 'package:dada/utils/image_utils.dart';
import 'package:dada/utils/toast_utils.dart';
import 'package:flutter/material.dart';
import 'package:flutter_image_compress/flutter_image_compress.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:path_provider/path_provider.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:video_compress/video_compress.dart';
import 'package:video_player/video_player.dart';
import 'package:wechat_assets_picker/wechat_assets_picker.dart';

class ReportPage extends StatefulWidget {
  final ReportType
      reportType; // 举报类型 : 1.聊天内容（聊天室内）; 2.用户资料；3.群; 4: 集结队；5：搭圈；6：市集；7：茶壶
  final String? userId;
  final String? postId;
  final String? groupId;
  final String? commentId;
  final String? roomId;

  const ReportPage(
      {super.key,
      required this.reportType,
      this.userId,
      this.postId,
      this.groupId,
      this.commentId,
      this.roomId});

  @override
  State<ReportPage> createState() => _ReportPageState();
}

class _ReportPageState extends State<ReportPage> {
  late List<String> reportReasonList;
  RxInt selectedReasonIndex = 0.obs;
  final TextEditingController textEditingController = TextEditingController();
  RxList<AssetEntity> selectedAssets = <AssetEntity>[].obs;
  PostType reportType = PostType.text;
  VideoPlayerController? _videoPlayerController;
  final RxBool _videoPlayerInitialized = false.obs;

  @override
  void initState() {
    super.initState();

    reportReasonList = [
      "知识侵权", //1.
      "欺诈骗钱", //2.
      "骚扰辱骂", //3.
      "广告营销", //4.
      "虚假资料", //5.
      "政治造谣", //6.
      "索要钱财", //7.
      "名称违规", //8.
      "色情低俗", //9.
      "昵称", //10.
      "头像", //11.
      "交友宣言", //12.
      "语音签名", //13.
      "相册",
      "对立引战",
      "血腥暴力",
      "涉及未成年人",
      "网络造谣",
      "其他",
    ];
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.colorFFF5F5F5,
      appBar: CustomAppBar(
        title: "举报",
      ),
      body: ListView(
        children: [
          _buildReportReasonWidget(),
          _buildReportDescriptionWidget(),
          _buildBottomBtn(),
        ],
      ),
    );
  }

  Widget _buildReportReasonWidget() {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 15.w),
      color: Colors.white,
      child: Column(
        children: [
          Row(
            children: [
              Text(
                "*",
                style: TextStyles.common(16.sp, AppColors.colorFFFA3535),
              ),
              Padding(
                padding: EdgeInsets.only(left: 4.w),
                child: Text(
                  "举报原因",
                  style: TextStyles.normal(16.sp),
                ),
              ),
            ],
          ),
          Padding(
            padding: EdgeInsets.only(top: 10.h, bottom: 15.h),
            child: Wrap(
              spacing: 7.5.w,
              runSpacing: 10.h,
              children: reportReasonList.map((e) {
                return Obx(
                  () {
                    int index = reportReasonList.indexOf(e);
                    bool selected = index == selectedReasonIndex.value;
                    return LabelItemWidget(
                      fontSize: 16.sp,
                      editable: false,
                      text: e,
                      minWidth: 110.w,
                      height: 35.h,
                      borderRadius: 35.h / 2,
                      borderColor: selected
                          ? AppColors.colorFF23AF28
                          : AppColors.colorFF999999,
                      textColor: selected
                          ? AppColors.colorFF23AF28
                          : AppColors.colorFF999999,
                      onTap: () {
                        selectedReasonIndex.value = index;
                      },
                    );
                  },
                );
              }).toList(),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildReportDescriptionWidget() {
    return Container(
      color: Colors.white,
      margin: EdgeInsets.only(top: 10.h),
      padding: EdgeInsets.symmetric(horizontal: 15.w),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: EdgeInsets.only(top: 10.h),
            child: Text(
              "举报描述",
              style: TextStyles.normal(16.sp),
            ),
          ),
          Container(
            width: ScreenUtil().screenWidth - 30.w,
            height: 120.h,
            margin: EdgeInsets.only(top: 10.h),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(5.r),
              color: AppColors.colorFFF5F5F5,
            ),
            child: CustomTextField.build(
              contentPadding: EdgeInsets.all(10.w),
              controller: textEditingController,
              maxLines: 1000,
              maxLength: 200,
              showLeftLength: true,
              // hintStyle: TextStyles.common(16.sp, AppColors.colorFF999999),
              hintText: "详细描述对方的违规行为，以便我们更快处理",
              leftCountColor: AppColors.colorFF999999,
            ),
          ),
          Padding(
            padding: EdgeInsets.only(top: 15.h),
            child: Row(
              children: [
                Text(
                  "添加证据",
                  style: TextStyles.normal(16.sp),
                ),
                Padding(
                  padding: EdgeInsets.only(left: 0.w),
                  child: Obx(
                    () => Text(
                      "（${selectedAssets.length}/9）",
                      style: TextStyles.common(12.sp, AppColors.colorFF999999),
                    ),
                  ),
                ),
              ],
            ),
          ),
          Padding(
            padding: EdgeInsets.only(top: 5.h),
            child: Text(
              "视频文件请上传小于35M的文件",
              style: TextStyles.common(12.sp, AppColors.colorFF999999),
            ),
          ),
          Padding(
            padding: EdgeInsets.only(top: 15.h),
            child: _buildAssetListWidget(),
          ),
        ],
      ),
    );
  }

  Widget _buildAssetListWidget() {
    return Container(
      padding: EdgeInsets.only(bottom: 25.h),
      child: Obx(() {
        List<AssetEntity> assets = <AssetEntity>[];
        if (reportType != PostType.video) {
          if (selectedAssets.length < 9) {
            assets.addAll(selectedAssets);
            assets.insert(0,
                const AssetEntity(id: "0", typeInt: -1, width: 0, height: 0));
          } else {
            assets = selectedAssets;
          }

          List<String> tags = [];
          List<Widget> children = [];
          for (int i = 0; i < assets.length; i++) {
            AssetEntity? asset = assets[i];
            if (asset.typeInt == -1) {
              children.add(
                _buildImageItemWidget(asset, i, "", tags),
              );
              continue;
            }
            String tag = HeroTagName.postImg.of("1000000_$i");
            tags.add(tag);
            children.add(
              _buildImageItemWidget(asset, i - 1, tag, tags),
            );
          }

          return Wrap(
            spacing: 5.w,
            runSpacing: 15.h,
            children: children,
          );
        } else {
          if (selectedAssets.isEmpty) {
            assets.add(
                const AssetEntity(id: "0", typeInt: -1, width: 0, height: 0));
          } else {
            assets = selectedAssets;
          }

          return Wrap(
            children: [
              _buildVideoItemWidget(assets.first),
            ],
          );
        }
      }),
    );
  }

  Widget _buildImageItemWidget(
      AssetEntity asset, int index, String tag, List<String> tags) {
    if (asset.typeInt == -1) {
      return _commonAddAssetsWidget(PostType.image);
    }
    return ClipRRect(
      borderRadius: BorderRadius.circular(10.r),
      child: Hero(
        tag: tag,
        child: GestureDetector(
          onTap: () async {
            ImageUtils.showImageBrowser(
              ImageBrowserArgs(
                tags,
                [],
                assets: selectedAssets,
                index: index,
              ),
            );
          },
          child: Stack(
            children: [
              Container(
                width: 105.w,
                height: 105.w,
                decoration: BoxDecoration(
                  image: DecorationImage(
                    fit: BoxFit.cover,
                    image: AssetEntityImageProvider(
                      asset,
                      isOriginal: false,
                    ),
                  ),
                ),
              ),
              Positioned(
                right: 0,
                top: 0,
                child: GestureDetector(
                  onTap: () {
                    selectedAssets.removeAt(index);
                    if (selectedAssets.isEmpty) {
                      reportType = PostType.text;
                    }
                  },
                  child: ImageUtils.getImage(
                      Assets.imagesDynamicPublishAssetsDelete, 20.w, 20.w),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildVideoItemWidget(AssetEntity asset) {
    if (asset.typeInt == -1 || _videoPlayerController == null) {
      initializeVideoPlayerController(asset);
      return _commonAddAssetsWidget(PostType.video);
    }

    String heroTag = HeroTagName.postVideo.name;
    return Hero(
      tag: heroTag,
      child: GestureDetector(
        onTap: () {
          ///跳转到全屏播放视频
          ImageUtils.showVideoPlayer(
              controller: _videoPlayerController!, heroTag: heroTag);
        },
        child: ClipRRect(
          borderRadius: BorderRadius.circular(10.r),
          child: Obx(
            () {
              return _videoPlayerInitialized.value == true
                  ? Stack(
                      alignment: Alignment.center,
                      children: [
                        SizedBox(
                          width: _videoPlayerController!.value.size.width >
                                  _videoPlayerController!.value.size.height
                              ? null
                              : 120.w,
                          height: _videoPlayerController!.value.size.width <
                                  _videoPlayerController!.value.size.height
                              ? null
                              : 120.h,
                          child: AspectRatio(
                            aspectRatio:
                                _videoPlayerController!.value.aspectRatio,
                            child: VideoPlayer(_videoPlayerController!),
                          ),
                        ),
                        ImageUtils.getImage(
                            Assets.imagesDynamicListCellVideoPlay, 30.w, 30.w),
                        Positioned(
                          right: 0,
                          top: 0,
                          child: GestureDetector(
                            onTap: () {
                              reportType = PostType.text;
                              selectedAssets.removeLast();
                              _videoPlayerController = null;
                            },
                            child: ImageUtils.getImage(
                                Assets.imagesDynamicPublishAssetsDelete,
                                20.w,
                                20.w),
                          ),
                        ),
                      ],
                    )
                  : Container();
            },
          ),
        ),
      ),
    );
  }

  Widget _commonAddAssetsWidget(PostType postType) {
    return GestureDetector(
      onTap: () async {
        ToastUtils.showBottomSheet(
          [S.current.album, S.current.camera],
          onTap: (index) async {
            if (index == 0) {
              PermissionStatus status =
                  await ImageUtils.androidIntSdkVersion() <= 32
                      ? await Permission.storage.status
                      : await Permission.photos.status;
              if (status.isGranted || status.isDenied) {
                if (GetPlatform.isAndroid) {
                  ToastUtils.showTopDialog(
                    barrierDismissible: false,
                    child: const PermissionUseDescDialog(
                        title: "相册权限说明",
                        desc: "获取图片/视频用于举报证据中添加证明材料，并上传图片/视频证明以供审核"),
                  );
                }
                status = await ImageUtils.androidIntSdkVersion() <= 32
                    ? await Permission.storage.request()
                    : await Permission.photos.request();
                if (Get.isDialogOpen == true) {
                  Get.back();
                }
                if (status != PermissionStatus.granted) {
                  return;
                }
              }
              List<AssetEntity>? assets = await ImagePickerUtil.selectAssets(
                  selectedAssets: selectedAssets,
                  maxAssets: postType == PostType.image ? 9 : 1);
              if (assets != null) {
                postType = assets.first.type == AssetType.image
                    ? PostType.image
                    : PostType.video;
                selectedAssets.value = assets;
              }
            } else {
              PermissionStatus status = await Permission.camera.status;
              if (status.isGranted || status.isDenied) {
                if (GetPlatform.isAndroid) {
                  ToastUtils.showTopDialog(
                    barrierDismissible: false,
                    child: const PermissionUseDescDialog(
                        title: "相机权限说明",
                        desc:
                            "拍摄图片/视频用于举报证据中添加证明材料, 并上传图片/视频证明以供审核, 拍摄后的图片将存放在系统照片中"),
                  );
                }
                status = await Permission.camera.request();
                if (Get.isDialogOpen == true) {
                  Get.back();
                }
                if (status != PermissionStatus.granted) {
                  return;
                }
              }
              AssetEntity? asset = await ImagePickerUtil.takeAsset(
                  enableRecording: postType == PostType.image ? false : true);
              if (asset != null) {
                postType = asset.type == AssetType.image
                    ? PostType.image
                    : PostType.video;
                if (postType == PostType.video) {
                  File? videoFile = await asset.originFile;
                  if (videoFile != null) {
                    int videoSize = await videoFile.length();
                    if (videoSize > 35 * 1024 * 1024) {
                      ToastUtils.showToast("视频超出最大限制35M，请重新选择");
                      return;
                    }
                  }
                  selectedAssets.clear();
                }
                selectedAssets.add(asset);
              }
            }
          },
        );
      },
      child: Container(
        width: 105.w,
        height: 105.w,
        alignment: Alignment.center,
        decoration: BoxDecoration(
          color: AppColors.colorFFF5F6F7,
          borderRadius: BorderRadius.circular(10.r),
        ),
        child:
            ImageUtils.getImage(Assets.imagesDynamicPublishAdd, 31.5.w, 31.5.w),
      ),
    );
  }

  Widget _buildBottomBtn() {
    return CommonGradientBtn(
      title: "提交",
      topMargin: 20.h,
      onTap: () async {
        if (selectedAssets.isEmpty) {
          ToastUtils.showToast("请提供证据图片或视频");
          return;
        }

        List<String> uploadedUrls = [];
        for (int i = 0; i < selectedAssets.length; i++) {
          AssetEntity asset = selectedAssets[i];
          File? file = await asset.originFile;

          String? assetUrl = file?.path;
          if (asset.type == AssetType.video) {
            if (assetUrl?.isNotEmpty == true) {
              VideoCompress.cancelCompression();
              MediaInfo? mediaInfo = await VideoCompress.compressVideo(
                  assetUrl!,
                  quality: VideoQuality.DefaultQuality,
                  deleteOrigin: false);
              if (mediaInfo?.file?.path.isNotEmpty == true) {
                assetUrl = mediaInfo?.file?.path;
              }
            }
          } else {
            if (assetUrl?.isNotEmpty == true) {
              int timestamp = DateTime.now().millisecondsSinceEpoch;
              var tmpDirectory = await getTemporaryDirectory();
              String tmpImgPath = "${tmpDirectory.path}/${timestamp}_$i.jpg";
              var compressedFile =
                  await FlutterImageCompress.compressAndGetFile(
                      assetUrl!, tmpImgPath,
                      quality: 90, rotate: 0);
              if (compressedFile?.path.isNotEmpty == true) {
                assetUrl = compressedFile?.path;
              }
            }
          }
          if (assetUrl != null) {
            String? uploadUrl = await ApiService().uploadFile(assetUrl);
            if (uploadUrl?.isNotEmpty == true) {
              uploadedUrls.add(uploadUrl!);
            }
          }
        }

        ///举报
        bool success = await ApiService().report(
          reasonType: selectedReasonIndex.value + 1,
          reportType: widget.reportType.index,
          businessId: widget.userId ??
              widget.groupId ??
              widget.postId ??
              widget.roomId ??
              widget.commentId,
          content: textEditingController.text,
          imgUrls: uploadedUrls,
        );
        if (success) {
          ToastUtils.showToast("举报成功");
          Get.back();
        }
      },
    );
  }

  ///初始化视频播放器
  Future<void> initializeVideoPlayerController(AssetEntity asset) async {
    if (asset.typeInt == -1) {
      return;
    }
    final String? url = await asset.getMediaUrl();
    if (url == null) {
      return;
    }
    final Uri uri = Uri.parse(url);
    if (Platform.isAndroid) {
      _videoPlayerController = VideoPlayerController.contentUri(uri);
    } else {
      _videoPlayerController = VideoPlayerController.file(File.fromUri(uri));
    }
    try {
      await _videoPlayerController?.initialize();
      _videoPlayerInitialized.value = true;
    } catch (e, s) {
      FlutterError.presentError(
        FlutterErrorDetails(
          exception: e,
          stack: s,
          library: "dada",
          silent: true,
        ),
      );
    } finally {
      if (mounted &&
          !context.debugDoingBuild &&
          context.owner?.debugBuilding != true) {
        // ignore: invalid_use_of_protected_member
        setState(() {});
      }
    }
  }
}
