import 'dart:async';
import 'dart:ui';

import 'package:dada/common/values/colors.dart';
import 'package:dada/common/values/enums.dart';
import 'package:dada/common/values/text_styles.dart';
import 'package:dada/common/values/themes.dart';
import 'package:dada/components/widgets/empty_widget.dart';
import 'package:dada/components/widgets/popup_menu_widget.dart';
import 'package:dada/components/widgets/sliver_header_delegate.dart';
import 'package:dada/generated/assets.dart';
import 'package:dada/generated/l10n.dart';
import 'package:dada/pages/dynamic/list/dynamic_list_page.dart';
import 'package:dada/pages/profile/user_profile_card_page.dart';
import 'package:dada/pages/profile/user_profile_controller.dart';
import 'package:dada/pages/profile/user_profile_header.dart';
import 'package:dada/pages/report/report_page.dart';
import 'package:dada/routers/router.dart';
import 'package:dada/services/network/api_service.dart';
import 'package:dada/services/user/user_service.dart';
import 'package:dada/utils/image_utils.dart';
import 'package:dada/utils/toast_utils.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

class UserProfilePage extends StatefulWidget {
  const UserProfilePage({super.key});

  @override
  State<UserProfilePage> createState() => _UserProfilePageState();
}

class _UserProfilePageState extends State<UserProfilePage>
    with TickerProviderStateMixin {
  final controller = UserProfileController();
  final RxDouble scrollPositionY = 0.0.obs;
  final RxDouble sliverAppBarOpacity = 0.0.obs;
  final double sliverAppBarMaxOffsetY = 36.h;
  final double topGradientBgHeight = 244.h;
  late PageController _pageController;
  late ScrollController _scrollController;
  Timer? _countDownTimer;
  RxInt applyLeftHours = 0.obs;

  @override
  void initState() {
    super.initState();

    String? userId = Get.parameters["userId"];
    controller.userId = userId;
    _pageController = PageController(initialPage: 0);
    _scrollController = ScrollController();
    _scrollController.addListener(() {
      debugPrint(
          'Scrolling updated. OffsetY: ${_scrollController.position.pixels}');
      if (_scrollController.position.pixels >= 0) {
        debugPrint(
            'Scrolling updated. OffsetY: ${_scrollController.position.pixels}');
        scrollPositionY.value = _scrollController.position.pixels;
        if (scrollPositionY.value > sliverAppBarMaxOffsetY) {
          scrollPositionY.value = sliverAppBarMaxOffsetY;
        }
        sliverAppBarOpacity.value =
            scrollPositionY.value / sliverAppBarMaxOffsetY;
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.themeData.colorScheme.surfaceTint,
      extendBodyBehindAppBar: true,
      body: GetBuilder(
        init: controller,
        global: false,
        builder: (ctrl) {
          if (ctrl.userInfoEntity.value == null) {
            return EmptyWidget();
          }
          return Stack(
            children: [
              ///模糊头像背景
              Obx(
                () => SizedBox(
                  width: ScreenUtil().screenWidth,
                  height: topGradientBgHeight,
                  child: ImageFiltered(
                    imageFilter: ImageFilter.blur(sigmaX: 15, sigmaY: 15),
                    child: ImageUtils.getImage(
                        controller.userInfoEntity.value?.avatar ?? "",
                        ScreenUtil().screenWidth,
                        topGradientBgHeight,
                        placeholder: Assets.imagesAvatarPlaceholder,
                        fit: BoxFit.fitWidth),
                  ),
                ),
              ),
              _buildCustomScrollView(),
            ],
          );
        },
      ),
    );
  }

  Widget _buildCustomScrollView() {
    final tabController = TabController(length: 2, vsync: this);
    return NestedScrollView(
      controller: _scrollController,
      physics: const ClampingScrollPhysics(),
      headerSliverBuilder: (BuildContext context, bool innerBoxIsScrolled) {
        return <Widget>[
          ///AppBar.
          Obx(() {
            return SliverAppBar(
              pinned: true,
              leading: IconButton(
                onPressed: () {
                  Get.back();
                },
                icon: Container(
                  width: 28.w,
                  height: 28.w,
                  alignment: Alignment.center,
                  child: sliverAppBarOpacity.value < 0.99
                      ? ImageUtils.getImage(Assets.imagesNaviBack, 8.w, 14.w)
                      : ImageUtils.getImage(Assets.imagesNaviBack, 8.w, 14.w),
                ),
              ),
              actions: [
                _buildRightBarBtn(),
              ],
              backgroundColor: sliverAppBarOpacity.value < 0.99
                  ? Colors.black.withOpacity(0.01)
                  : AppTheme.themeData.colorScheme.surfaceTint
                      .withOpacity(sliverAppBarOpacity.value),
            );
          }),

          ///顶部用户信息
          Obx(
            () => SliverPersistentHeader(
              pinned: false,
              delegate: SliverHeaderDelegate(
                minHeight: controller.sliverHeaderHeight.value,
                maxHeight: controller.sliverHeaderHeight.value,
                child: Container(
                  color: Colors.transparent,
                  child: UserProfileHeader(
                    ctrl: controller,
                    heightUpdate: (height) {
                      double headerHeight =
                          controller.sliverHeaderHeight.value - 21.h + height;
                      controller.sliverHeaderHeight.value = headerHeight;
                    },
                  ),
                ),
              ),
            ),
          ),

          ///个人信息、动态分栏
          _buildTabBar(tabController),
        ];
      },
      body: Container(
        color: AppTheme.themeData.colorScheme.surfaceTint,
        child: Column(
          children: [
            Expanded(
              child: PageView(
                controller: _pageController,
                onPageChanged: (index) {
                  tabController.animateTo(index);
                },
                children: [
                  UserProfileCardPage(ctrl: controller),
                  DynamicListWidget(
                    dynamicType: DynamicType.mine,
                    userId: controller.userId,
                  ),
                ],
              ),
            ),
            Visibility(
              visible: controller.userId != null && controller.userId != UserService().user?.id,
              child: Container(
                height: 56.h + ScreenUtil().bottomBarHeight,
                padding: EdgeInsets.only(bottom: ScreenUtil().bottomBarHeight),
                color: Colors.white,
                child: _buildBottomBtnWidget(),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTabBar(TabController tabController) {
    return Obx(() {
      bool isMySelf =
          controller.userInfoEntity.value?.id == UserService().user?.id;
      return SliverPersistentHeader(
        pinned: true,
        delegate: SliverHeaderDelegate(
          minHeight: 41.h,
          maxHeight: 41.h,
          child: Container(
            color: AppTheme.themeData.colorScheme.surfaceTint,
            padding: EdgeInsets.only(top: 5.h, bottom: 5.h),
            child: TabBar(
              padding: EdgeInsets.only(left: 15.w),
              tabAlignment: TabAlignment.start,
              controller: tabController,
              tabs: [
                Tab(
                    text: isMySelf
                        ? S.current.aboutMe
                        : controller.userInfoEntity.value?.sex == 0
                            ? "${S.current.about}${S.current.he}"
                            : "${S.current.about}${S.current.her}"),
                Tab(text: S.current.dynamic),
              ],
              dividerColor: Colors.transparent,
              isScrollable: true,
              labelPadding: EdgeInsets.only(right: 20.w),
              labelStyle: TextStyles.medium(18.sp),
              labelColor: AppTheme.themeData.textTheme.headlineLarge?.color,
              unselectedLabelColor:
                  AppTheme.themeData.textTheme.bodyMedium?.color,
              unselectedLabelStyle: TextStyle(fontSize: 16.sp),
              indicatorWeight: 6.h,
              indicatorPadding: EdgeInsets.only(
                  bottom: 8.h, top: 15.h, left: 6.w, right: 6.w),
              indicator: BoxDecoration(
                color: Theme.of(context)
                    .bottomNavigationBarTheme
                    .selectedLabelStyle
                    ?.color,
                borderRadius: BorderRadius.zero,
              ),
              onTap: (index) {
                _pageController.animateToPage(index,
                    duration: const Duration(milliseconds: 200),
                    curve: Curves.easeInCubic);
              },
            ),
          ),
        ),
      );
    });
  }

  Widget _buildBottomBtnWidget() {
    return Obx(() {
      bool isFriend = controller.userInfoEntity.value?.isFriend == 1;
      return Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Visibility(
            visible: !isFriend,
            child: GestureDetector(
              onTap: () {
                Get.toNamed(GetRouter.addFriendSend, parameters: {
                  "userId": controller.userInfoEntity.value!.id!,
                  "avatar": controller.userInfoEntity.value!.avatar!,
                  "nickname": controller.userInfoEntity.value!.nickname!,
                });
              },
              child: Container(
                width: 100.w,
                height: 40.h,
                margin: EdgeInsets.only(right: 5.w),
                alignment: Alignment.center,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(20.r),
                  border: Border.all(color: AppColors.colorFF23AF28),
                ),
                child: Text(
                  "加为好友",
                  style: TextStyles.common(16.sp, AppColors.colorFF23AF28),
                ),
              ),
            ),
          ),
          GestureDetector(
            onTap: () {
              controller.sendMsgToUser();
            },
            child: Container(
              width: 100.w,
              height: 40.h,
              margin: EdgeInsets.only(right: 5.w),
              alignment: Alignment.center,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(20.r),
                border: Border.all(color: AppColors.colorFF23AF28),
              ),
              child: Text(
                "去聊天",
                style: TextStyles.common(16.sp, AppColors.colorFF23AF28),
              ),
            ),
          ),
        ],
      );
    });
  }

  void startCountDownTimer() {
    if (_countDownTimer != null) {
      return;
    }
    _countDownTimer = Timer.periodic(const Duration(hours: 1), (_) {
      setState(() {
        applyLeftHours--;
        if (applyLeftHours.value <= 0) {
          _countDownTimer?.cancel();
          _countDownTimer = null;
        }
      });
    });
  }

  void cancelCountDownTimer() {
    _countDownTimer?.cancel();
    _countDownTimer = null;
  }

  Widget _buildRightBarBtn() {
    return Visibility(
      visible: controller.userId != UserService().user?.id,
      child: PopupMenuWidget(
        offset: Offset(20.w, 20.w),
        titles: const ["举报", "拉黑"],
        onSelected: (value) async {
          if (value == 0) {
            //举报
            Get.to(() => ReportPage(
                reportType: ReportType.user, userId: controller.userId!));
          } else if (value == 1) {
            //拉黑
            bool success =
                await ApiService().addToBlackList(controller.userId!);
            if (success) {
              ToastUtils.showToast("拉黑成功");
            }
          }
        },
        child: Padding(
          padding: EdgeInsets.symmetric(horizontal: 10.w),
          child: Icon(
            Icons.more_vert,
            color:
                sliverAppBarOpacity.value < 0.99 ? Colors.white : Colors.black,
          ),
        ),
      ),
    );
  }

  @override
  void dispose() {
    cancelCountDownTimer();
    super.dispose();
  }
}
