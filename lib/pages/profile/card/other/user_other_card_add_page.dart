import 'package:dada/common/values/colors.dart';
import 'package:dada/common/values/text_styles.dart';
import 'package:dada/common/values/themes.dart';
import 'package:dada/components/widgets/common_gradient_btn.dart';
import 'package:dada/components/widgets/custom_app_bar.dart';
import 'package:dada/components/widgets/custom_text_field.dart';
import 'package:dada/components/widgets/permission_use_desc_dialog.dart';
import 'package:dada/generated/assets.dart';
import 'package:dada/generated/l10n.dart';
import 'package:dada/model/user_id_card_entity.dart';
import 'package:dada/services/network/api_service.dart';
import 'package:dada/utils/image_utils.dart';
import 'package:dada/utils/toast_utils.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:image_picker/image_picker.dart';
import 'package:permission_handler/permission_handler.dart';

class UserOtherCardAddPage extends StatefulWidget {
  const UserOtherCardAddPage({super.key});

  @override
  State<UserOtherCardAddPage> createState() => _UserOtherCardAddPageState();
}

class _UserOtherCardAddPageState extends State<UserOtherCardAddPage> {
  TextEditingController editingController = TextEditingController();
  RxString selectedImagePath = "".obs;

  UserIdCardEntity? cardInfo;
  String? title;
  String? cardType;

  @override
  void initState() {
    super.initState();

    title = Get.parameters["title"];
    cardType = Get.parameters["cardType"];
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.themeData.scaffoldBackgroundColor,
      appBar: CustomAppBar(title: title),
      body: Container(
        color: AppTheme.themeData.colorScheme.surfaceTint,
        child: Column(
          children: [
            Container(
              margin: EdgeInsets.only(top: 20.h, left: 15.w, right: 15.w),
              padding: EdgeInsets.only(
                  top: 25.h, left: 18.w, right: 18.w, bottom: 24.h),
              decoration: BoxDecoration(
                color: AppTheme.themeData.colorScheme.primary,
                borderRadius: BorderRadius.circular(10.r),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    crossAxisAlignment: CrossAxisAlignment.end,
                    children: [
                      Padding(
                        padding: EdgeInsets.only(
                          left: 5.5.w,
                        ),
                        child: Text(
                          S.current.picName,
                          style: TextStyles.normal(16.sp),
                        ),
                      ),
                      Text(
                        S.current.picNameTip,
                        style: TextStyles.normal(14.sp,
                            c: AppColors.colorFF999999),
                      ),
                    ],
                  ),
                  SizedBox(
                    height: 10.h,
                  ),
                  Container(
                    height: 30.h,
                    margin: EdgeInsets.symmetric(horizontal: 5.5.w),
                    decoration: BoxDecoration(
                      color: AppColors.colorFFF5F5F5,
                      borderRadius: BorderRadius.circular(5.r),
                      border: Border.all(
                          color: AppColors.colorFF999999, width: 1.h),
                    ),
                    child: Padding(
                      padding: EdgeInsets.only(top: 3.h, bottom: 3.h),
                      child: CustomTextField.build(
                        contentPadding: EdgeInsets.only(left: 6.w, right: 6.w),
                        controller: editingController,
                      ),
                    ),
                  ),
                  Padding(
                    padding: EdgeInsets.only(top: 20.h, left: 11.w),
                    child: Text(
                      S.current.screenshot,
                      style: TextStyles.normal(16.sp),
                    ),
                  ),
                  Padding(
                    padding: EdgeInsets.only(left: 12.w, top: 10.h),
                    child: Obx(() {
                      if (selectedImagePath.value.isNotEmpty) {
                        return SizedBox(
                          width: 80.w,
                          height: 80.w,
                          child: ClipRRect(
                            borderRadius: BorderRadius.circular(5.r),
                            child: Stack(
                              alignment: Alignment.topRight,
                              children: [
                                ImageUtils.getImage(
                                    selectedImagePath.value, 80.w, 80.w,
                                    fit: BoxFit.cover),
                                GestureDetector(
                                  onTap: () {
                                    selectedImagePath.value = "";
                                  },
                                  child: ImageUtils.getImage(
                                      Assets.imagesUserLabelImgDelete,
                                      20.w,
                                      18.h),
                                ),
                              ],
                            ),
                          ),
                        );
                      }
                      return GestureDetector(
                        onTap: () {
                          String titleType =
                              (title ?? "").replaceAll(S.current.add, '');
                          ToastUtils.showBottomSheet(
                            [S.current.album, S.current.camera],
                            onTap: (index) async {
                              if (index == 0) {
                                PermissionStatus status =
                                    await ImageUtils.androidIntSdkVersion() <=
                                            32
                                        ? await Permission.storage.status
                                        : await Permission.photos.status;
                                if (status.isGranted || status.isDenied) {
                                  if (GetPlatform.isAndroid) {
                                    ToastUtils.showTopDialog(
                                      barrierDismissible: false,
                                      child: PermissionUseDescDialog(
                                          title: "相册权限说明",
                                          desc: "获取图片用于更换$titleType信息"),
                                    );
                                  }
                                  status =
                                      await ImageUtils.androidIntSdkVersion() <=
                                              32
                                          ? await Permission.storage.request()
                                          : await Permission.photos.request();
                                  if (Get.isDialogOpen == true) {
                                    Get.back();
                                  }
                                  if (status != PermissionStatus.granted) {
                                    return;
                                  }
                                }
                              } else if (index == 1) {
                                PermissionStatus status =
                                    await Permission.camera.status;
                                if (status.isGranted || status.isDenied) {
                                  if (GetPlatform.isAndroid) {
                                    ToastUtils.showTopDialog(
                                      barrierDismissible: false,
                                      child: PermissionUseDescDialog(
                                          title: "相机权限说明",
                                          desc:
                                              "拍摄图片用于更换$titleType信息, 拍摄后的图片将存放在系统照片中"),
                                    );
                                  }
                                  status = await Permission.camera.request();
                                  if (Get.isDialogOpen == true) {
                                    Get.back();
                                  }
                                  if (status != PermissionStatus.granted) {
                                    return;
                                  }
                                }
                              }
                              XFile? imageFile = await ImagePicker().pickImage(
                                  source: index == 0
                                      ? ImageSource.gallery
                                      : ImageSource.camera);
                              if (imageFile != null) {
                                selectedImagePath.value = imageFile.path;
                              }
                            },
                          );
                        },
                        child: ImageUtils.getImage(
                            Assets.imagesUserOtherCardEditPicAdd, 80.w, 80.w),
                      );
                    }),
                  ),
                  _buildBottomBtn(),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildBottomBtn() {
    return CommonGradientBtn(
      horizontalMargin: 5.w,
      topMargin: 30.h,
      bottomMargin: 15.h,
      title: S.current.ok,
      onTap: () {
        _postAddCardRequest();
      },
    );
  }

  void _postAddCardRequest() async {
    if (editingController.text.isEmpty) {
      ToastUtils.showToast(S.current.addOtherCardNameEmptyTip);
      return;
    }

    if (selectedImagePath.value.isEmpty) {
      ToastUtils.showToast(S.current.addOtherCardImageEmptyTip);
      return;
    }

    String? imageUrl = await ApiService().uploadFile(selectedImagePath.value);
    bool success = await ApiService().addUserCard(
        cardType: cardType,
        cardName: editingController.text,
        cardUrl: imageUrl);
    ApiService().doTask(taskId: "7");
    if (success) {
      Get.back(result: 1);
    }
  }
}
