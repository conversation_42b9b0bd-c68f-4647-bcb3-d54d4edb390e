import 'package:dada/model/small_room_detail_info_entity.dart';
import 'package:dada/model/user_card_box_entity.dart';
import 'package:dada/model/user_info_entity.dart';
import 'package:dada/pages/recharge/dialog/month_card_expired_limit_chat_dialog.dart';
import 'package:dada/routers/router.dart';
import 'package:dada/services/im/chat_im_manager.dart';
import 'package:dada/services/network/api_service.dart';
import 'package:dada/services/user/user_service.dart';
import 'package:dada/utils/audio_player_utils.dart';
import 'package:dada/utils/string_util.dart';
import 'package:dada/utils/toast_utils.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:tencent_cloud_chat_sdk/models/v2_tim_conversation.dart';

class UserProfileController extends GetxController
    with GetSingleTickerProviderStateMixin {
  final Rx<UserInfoEntity?> userInfoEntity = UserInfoEntity().obs;
  String? userId;

  final double kMaxInfoContainerHeight = 170.h;
  final double kMaxSliverHeaderHeight = 289.h;
  final RxDouble containerHeight = 0.0.h.obs;
  final RxDouble sliverHeaderHeight = 0.0.h.obs;

  final AudioPlayerUtils audioPlayer = AudioPlayerUtils();
  final Rx<Duration?> audioDuration = const Duration().obs;
  final RxBool audioPlaying = false.obs;

  final RxBool labelsUnfold = false.obs;

  RxList<UserCardBoxEntity> cardBoxList = <UserCardBoxEntity>[].obs;

  String? dadaRoomId;
  SmallRoomDetailInfoEntity? roomInfo;

  @override
  void onInit() async {
    super.onInit();

    containerHeight.value = kMaxInfoContainerHeight;
    sliverHeaderHeight.value = kMaxSliverHeaderHeight;
    await onLoad();
  }

  Future<void> onLoad() async {
    await getRoomInfo();
    await loadUserInfo();
    // updateSliverHeaderHeight();
    loadUserCardBoxList();
    await _setAudioPlayer();
  }

  Future<void> getRoomInfo() async {
    userId ??= UserService().user?.id;
    roomInfo =
        await ApiService().getSmallRoomDetailRoomInfo(userId: userId ?? '');
    dadaRoomId = roomInfo?.room?.dadaRoomId;
    update();
  }

  Future<void> loadUserInfo({bool? showLoading = true}) async {
    userId ??= UserService().user?.id;
    userInfoEntity.value =
        await ApiService().getUserInfo(userId!, showLoading: showLoading);
    if (userInfoEntity.value?.txtSignature != null &&
        userInfoEntity.value!.txtSignature!.isNotEmpty) {
      containerHeight.value = kMaxInfoContainerHeight + 88.h;
      sliverHeaderHeight.value = kMaxSliverHeaderHeight + 88.h;
    } else {
      updateSliverHeaderHeight();
    }
    update();
  }

  void loadUserCardBoxList() async {
    cardBoxList.value =
        await ApiService().getUserCardBoxCardCount(userId!) ?? [];
  }

  Future<void> _setAudioPlayer() async {
    if (userInfoEntity.value?.voiceSignature != null) {
      audioDuration.value =
          await audioPlayer.setUrl(userInfoEntity.value!.voiceSignature!);
      audioPlayer.stateStream?.listen((event) {
        if (event == AudioPlayerState.playing) {
          audioPlaying.value = true;
        } else {
          audioPlaying.value = false;
        }
      });
    }
  }

  void updateSliverHeaderHeight() {
    if (userInfoEntity.value?.txtSignature == null ||
        userInfoEntity.value?.txtSignature?.isEmpty == true) {
      sliverHeaderHeight.value = kMaxSliverHeaderHeight - 18.h;
      containerHeight.value = kMaxInfoContainerHeight - 18.h;
    } else {
      sliverHeaderHeight.value = kMaxSliverHeaderHeight;
      containerHeight.value = kMaxInfoContainerHeight;
    }
  }

  void sendMsgToUser() async {
    if (!UserService().checkIsMonthCardUser()) {
      ToastUtils.showBottomDialog(const MonthCardExpiredLimitChatDialog());
    } else {
      V2TimConversation conversation = await ChatIMManager.sharedInstance
          .getConversation(userID: userId, type: 1);
      Get.toNamed(GetRouter.chatDetail, arguments: conversation);
    }
  }

  ///开始了解
  Future<bool> startKnown() async {
    bool success = await ApiService().startKnown(userId: userId!);
    if (success) {
      await loadUserInfo(showLoading: false);
    }
    return success;
  }

  ///申请搭子
  Future<bool> applyDazi(int daziType) async {
    bool success =
        await ApiService().applyDazi(userId: userId!, friendType: daziType);
    if (success) {
      await loadUserInfo(showLoading: false);
    }
    return success;
  }

  void updateUserInfo(
      {String? text, String? audioPath, int? audioLength}) async {
    if (text != null) {
      await ApiService().editUserInfo(textSignature: text);
    } else if (audioPath != null) {
      String? audioUrl = await ApiService().uploadFile(audioPath);
      if (audioUrl != null && audioUrl.isNotEmpty) {
        await ApiService()
            .editUserInfo(audioSignature: audioUrl, voiceLength: audioLength);
      }
    }
    await loadUserInfo();
    if (audioPath != null) {
      await _setAudioPlayer();
    }
  }

  void deleteAudio() async {
    if (audioPlaying.value == true) {
      await audioPlayer.stop();
    }
    await ApiService().editUserInfo(audioSignature: null, voiceLength: 0);
    loadUserInfo();
  }

  @override
  void onClose() {
    audioPlayer.cancelPlayerSubscriptions();
    audioPlayer.dispose();
    super.onClose();
  }
}
