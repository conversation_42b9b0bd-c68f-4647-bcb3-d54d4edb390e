import 'package:dada/common/values/colors.dart';
import 'package:dada/common/values/text_styles.dart';
import 'package:dada/components/widgets/common_gradient_btn.dart';
import 'package:dada/components/widgets/custom_app_bar.dart';
import 'package:dada/components/widgets/empty_widget.dart';
import 'package:dada/configs/app_config.dart';
import 'package:dada/generated/assets.dart';
import 'package:dada/model/goods_entity.dart';
import 'package:dada/pages/recharge/recharge_controller.dart';
import 'package:dada/pages/store/pay_order_info_page.dart';
import 'package:dada/routers/router.dart';
import 'package:dada/services/user/user_service.dart';
import 'package:dada/utils/image_utils.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

class RechargeMonthCardPage extends StatelessWidget {
  RechargeMonthCardPage({super.key});

  final RechargeController controller = Get.put(RechargeController())..type = 0;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: CustomAppBar(title: "购买月卡"),
      body: Column(
        children: [
          Expanded(child: _buildGoodsList(context)),
          _buildBottomButton()
        ],
      ),
    );
  }

  Widget _buildGoodsList(BuildContext context) {
    return GetBuilder(
        init: controller,
        builder: (controller) {
          if (controller.goodsList.isEmpty) {
            return EmptyWidget();
          }
          return GridView.extent(
            padding: EdgeInsets.only(left: 15.w, right: 15.w, top: 15.h),
            maxCrossAxisExtent: 110.w,
            mainAxisSpacing: 10.w,
            crossAxisSpacing: 6.8.w,
            childAspectRatio: 110.w / 158.w,
            children: controller.goodsList.map((e) {
              int index = controller.goodsList.indexOf(e);
              return _buildRechargeListItemWidget(e, index);
            }).toList(),
          );
        });
  }

  Widget _buildBottomButton() {
    return GetBuilder(
      init: controller,
      filter: (controller) => controller.goodsList.length,
      builder: (controller) {
        if (controller.goodsList.isEmpty) {
          return Container();
        }
        return Obx(() {
          GoodsEntity goodsEntity =
              controller.goodsList[controller.currentSelectedIndex.value];
          bool enabled = true;
          if (goodsEntity.isBuy != null &&
              goodsEntity.buyLimit != null &&
              goodsEntity.isBuy! >= goodsEntity.buyLimit!) {
            enabled = false;
          }
          return Container(
            constraints:
                BoxConstraints(minHeight: 138.w, minWidth: double.infinity),
            color: Colors.black.withOpacity(0.05),
            padding: EdgeInsets.symmetric(horizontal: 15.w),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                Text(
                  controller.goodsList[controller.currentSelectedIndex.value]
                          .goodsDescription ??
                      "",
                  style: TextStyle(
                      fontSize: 14.sp, color: AppColors.colorFF666666),
                ),
                CommonGradientBtn(
                  title: "确认协议并购买",
                  width: 300.w,
                  height: 45.w,
                  topMargin: 10.h,
                  enabled: enabled,
                  disabledImage: Assets.imagesGradientBtnDisabledBg1,
                  onTap: () {
                    if (!enabled) {
                      return;
                    }
                    Get.to(
                      () => PayOrderInfoPage(
                          goodsEntity: controller.goodsList[
                              controller.currentSelectedIndex.value]),
                    )?.then((result) {
                      if (result == "1") {
                        controller.loadData();
                        controller.loadUserStainsCount();
                        UserService().refresh();
                        Get.back(result: "1");
                      }
                    });
                  },
                ),

                Container(
                  margin: EdgeInsets.only(top: 12.h, bottom: 20.h),
                  child: Text.rich(
                    TextSpan(
                      children: [
                        TextSpan(
                          text: "购买即同意",
                          style: TextStyle(
                            color: AppColors.colorFF999999,
                            fontSize: 12.sp,
                          ),
                        ),
                        TextSpan(
                          text: "《搭吖会员协议》",
                          style: TextStyle(
                            color: AppColors.colorFF6265FF,
                            fontSize: 12.sp,
                            decoration: TextDecoration.underline,
                          ),
                          recognizer: TapGestureRecognizer()
                            ..onTap = () {
                              Get.toNamed(GetRouter.webView, parameters: {
                                "url": AppConfig.vipAgreementUrl
                              });
                            },
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          );
        });
      },
    );
  }

  Widget _buildRechargeListItemWidget(GoodsEntity itemEntity, int index) {
    return Obx(() {
      bool selected = controller.currentSelectedIndex.value == index;
      String bgImage = Assets.imagesRechargeListItemBg3;

      return GestureDetector(
        onTap: () {
          controller.currentSelectedIndex.value = index;
        },
        child: Container(
          width: 110.w,
          height: 158.w,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(12.r),
            border: Border.all(
                color: selected ? AppColors.colorFF23AF28 : Colors.transparent,
                width: 3.w,
                strokeAlign: BorderSide.strokeAlignOutside),
            image:
                DecorationImage(image: AssetImage(bgImage), fit: BoxFit.cover),
          ),
          child: Column(
            children: [
              Expanded(
                child: Stack(
                  clipBehavior: Clip.none,
                  alignment: Alignment.topCenter,
                  children: [
                    ImageUtils.getImage(itemEntity.image ?? "", 110.w, 130.w,
                        fit: BoxFit.contain),
                    if (itemEntity.image?.isNotEmpty != true &&
                        itemEntity.goodsName?.isNotEmpty == true)
                      Center(
                          child: Text(
                        itemEntity.goodsName!,
                        style: TextStyle(
                            fontSize: 14.sp, color: AppColors.colorFF3D3D3D),
                      )),
                    if (itemEntity.goodsType == 1 &&
                        itemEntity.endTime?.isNotEmpty == true)
                      _buildDeadLine(itemEntity),
                    if ((itemEntity.buyLimit ?? 0) > 0)
                      _buildBuyLimit(itemEntity)
                  ],
                ),
              ),
              Container(
                alignment: Alignment.centerLeft,
                height: 28.w,
                padding: EdgeInsets.only(top: 5.h, left: 10.w),
                decoration: BoxDecoration(
                  gradient: const LinearGradient(
                    colors: [AppColors.colorFFA0F6A5, AppColors.colorFF58C75D],
                    begin: Alignment.topLeft,
                    end: Alignment.topRight,
                  ),
                  borderRadius: BorderRadius.only(
                      bottomLeft: Radius.circular(12.r),
                      bottomRight: Radius.circular(12.r)),
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      "￥${itemEntity.price}",
                      style: TextStyles.medium(16.sp),
                    ),
                    Padding(
                      padding: EdgeInsets.only(right: 0.w),
                      child: Text(
                        "￥${itemEntity.originalPrice}",
                        style: TextStyle(
                          fontSize: 14.sp,
                          fontWeight: FontWeight.w600,
                          color: AppColors.colorFF666666,
                          decoration: TextDecoration.lineThrough,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      );
    });
  }

  Widget _buildDeadLine(GoodsEntity itemEntity) {
    return Align(
      alignment: Alignment.topRight,
      child: Container(
        decoration: BoxDecoration(
            gradient: const LinearGradient(
                colors: [AppColors.colorFFE8F0AD, AppColors.colorFFFFD470]),
            borderRadius: BorderRadius.only(
                bottomLeft: Radius.circular(12.r),
                topRight: Radius.circular(12.r))),
        width: 85.w,
        height: 25.w,
        alignment: Alignment.center,
        child: Builder(builder: (context) {
          var dateTime = DateTime.parse(itemEntity.endTime!);
          return Text(
            "截止到${dateTime.month}/${dateTime.day}",
            style: TextStyle(fontSize: 12.sp, color: AppColors.colorFF666666),
          );
        }),
      ),
    );
  }

  Widget _buildBuyLimit(GoodsEntity itemEntity) {
    return Align(
        alignment: Alignment.centerRight,
        child: Container(
          margin: EdgeInsets.only(top: 20.w),
          padding: EdgeInsets.symmetric(horizontal: 2.w, vertical: 5.h),
          decoration: BoxDecoration(
              color: AppColors.colorFFFF6464,
              borderRadius: BorderRadius.only(
                  bottomLeft: Radius.circular(12.r),
                  topLeft: Radius.circular(12.r))),
          child: Text("限\n购\n${itemEntity.isBuy ?? 0}/${itemEntity.buyLimit}",
              textAlign: TextAlign.center,
              style: TextStyle(fontSize: 11.sp, color: Colors.white)),
        ));
  }
}
