import 'dart:ui' as ui show PlaceholderAlignment;
import 'package:dada/common/values/colors.dart';
import 'package:dada/common/values/enums.dart';
import 'package:dada/common/values/text_styles.dart';
import 'package:dada/components/widgets/common_gradient_btn.dart';
import 'package:dada/components/widgets/gradient_widget.dart';
import 'package:dada/generated/assets.dart';
import 'package:dada/utils/image_utils.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

class PayStatusBottomDialog extends StatefulWidget {
  final PayResultStatus payResultStatus;
  final PayType payType;
  final double price;

  const PayStatusBottomDialog(
      {super.key,
      required this.payResultStatus,
      required this.payType,
      required this.price});

  @override
  State<PayStatusBottomDialog> createState() => _PayStatusBottomDialogState();
}

class _PayStatusBottomDialogState extends State<PayStatusBottomDialog> {
  @override
  Widget build(BuildContext context) {
    return GradientWidget(
      width: ScreenUtil().screenWidth,
      height: 472.h,
      colors: const [
        AppColors.colorFFD2F6C0,
        Colors.white,
      ],
      stops: const [0, 0.35],
      borderRadius: BorderRadius.only(
        topLeft: Radius.circular(20.r),
        topRight: Radius.circular(20.r),
      ),
      child: Stack(
        children: [
          _buildContainer(),
          Positioned(
            right: 15.w,
            top: 15.w,
            child: GestureDetector(
              onTap: () {
                Get.back();
              },
              child: Icon(
                Icons.close,
                size: 20.w,
                color: AppColors.colorFF666666,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildContainer() {
    bool isSuccess = widget.payResultStatus == PayResultStatus.success;
    return Column(
      children: [
        Padding(
          padding: EdgeInsets.only(top: 50.h),
          child: ImageUtils.getImage(
              isSuccess
                  ? Assets.imagesPayResultSuccessIcon
                  : Assets.imagesPayResultFailedIcon,
              50.w,
              50.w),
        ),
        Padding(
          padding: EdgeInsets.only(top: 5.h),
          child: Text(
            isSuccess ? "支付成功" : "支付失败",
            style: TextStyles.normal(14.sp),
          ),
        ),
        _buildPriceWidget(),
        _buildPayTypeTitleWidget(),
        const Spacer(),
        _buildBottomBtn(),
      ],
    );
  }

  ///价格
  Widget _buildPriceWidget() {
    double price = widget.price;
    return Padding(
      padding: EdgeInsets.only(top: 10.h),
      child: Text.rich(
        TextSpan(
          children: [
            WidgetSpan(
              child: Padding(
                padding: EdgeInsets.only(bottom: 6.h),
                child: Text(
                  "￥",
                  style: TextStyles.normal(16.sp),
                ),
              ),
            ),
            WidgetSpan(
              alignment: ui.PlaceholderAlignment.bottom,
              child: Padding(
                padding: EdgeInsets.only(left: 3.w),
                child: Text(
                  price.toStringAsFixed(2),
                  style: TextStyles.bold(30.sp, w: FontWeight.w700),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPayTypeTitleWidget() {
    String payTypeStr = "支付宝支付";
    if (widget.payType == PayType.alipay) {
      payTypeStr = "支付宝支付";
    } else if (widget.payType == PayType.wechat) {
      payTypeStr = "微信支付";
    } else if (widget.payType == PayType.apple) {
      payTypeStr = "苹果支付";
    }
    return Padding(
      padding: EdgeInsets.only(top: 16.h),
      child: Text(
        payTypeStr,
        style: TextStyles.common(14.sp, AppColors.colorFF666666),
      ),
    );
  }

  Widget _buildBottomBtn() {
    String title = "知道了";
    if (widget.payResultStatus == PayResultStatus.success) {
      title = "知道了";
    } else {
      title = "关闭";
    }
    return CommonGradientBtn(
      title: title,
      bottomMargin: ScreenUtil().bottomBarHeight + 15.h,
      onTap: () {
        Get.back();
      },
    );
  }
}
