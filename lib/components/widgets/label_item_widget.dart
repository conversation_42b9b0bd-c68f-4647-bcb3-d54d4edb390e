import 'package:dada/common/values/colors.dart';
import 'package:dada/common/values/text_styles.dart';
import 'package:dada/generated/l10n.dart';
import 'package:dada/components/widgets/input_text_page.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

class LabelItemWidget extends StatelessWidget {
  final String text;
  final double? height;
  final double? minWidth;
  final double? borderRadius;
  final Color? bgColor;
  final Color? borderColor;
  final Color? textColor;
  final bool? editable;
  final bool? isAddItem;
  final double? fontSize;
  final double? closeBtnSize;
  final MainAxisAlignment? alignment;
  final Color? closeBtnColor;
  final int? textMaxLength;
  final int? addTextMaxLength;
  final EdgeInsets? padding;
  final bool? recommendWhenAddAction;
  final Function(String)? addAction;
  final Function()? deleteAction;
  final Function()? onTap;

  const LabelItemWidget({
    super.key,
    required this.text,
    this.height,
    this.minWidth,
    this.borderRadius,
    this.bgColor,
    this.borderColor,
    this.textColor,
    this.alignment,
    this.editable = true,
    this.isAddItem = false,
    this.fontSize,
    this.closeBtnSize,
    this.closeBtnColor,
    this.textMaxLength,
    this.addTextMaxLength = 15,
    this.padding,
    this.addAction,
    this.deleteAction,
    this.onTap,
    this.recommendWhenAddAction,
  });

  @override
  Widget build(BuildContext context) {
    if (isAddItem == true) {
      return GestureDetector(
        onTap: () {
          Get.to(
            () => InputTextPage(
              title: S.current.addLabel,
              showRecommend: recommendWhenAddAction ?? false,
              callback: (text) {
                addAction?.call(text);
              },
              maxLength: addTextMaxLength,
            ),
          );
        },
        child: Container(
          height: height ?? 30.h,
          padding: padding ?? EdgeInsets.symmetric(horizontal: 10.w),
          decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(borderRadius ?? 5.r),
              border: Border.all(color: borderColor ?? AppColors.colorFFD8D8D8),
              color: bgColor),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Icon(
                Icons.add,
                color: textColor,
                size: 15.w,
              ),
              SizedBox(
                width: 2.w,
              ),
              Text(
                text,
                style: TextStyles.common(fontSize ?? 14.sp, textColor),
              ),
            ],
          ),
        ),
      );
    }
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: padding ?? EdgeInsets.only(left: 10.w, right: 10.w),
        height: height ?? 30.h,
        constraints: BoxConstraints(
          minWidth: minWidth ?? 70.w,
        ),
        decoration: BoxDecoration(
          border: Border.all(
              color: borderColor ?? AppColors.colorFFD8D8D8, width: 1.w),
          borderRadius: BorderRadius.circular(borderRadius ?? 5.r),
          color: bgColor,
        ),
        child: Row(
          mainAxisAlignment: alignment ?? MainAxisAlignment.center,
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              textMaxLength != null
                  ? text.length > textMaxLength!
                      ? "${text.substring(0, textMaxLength!)}..."
                      : text
                  : text,
              overflow: TextOverflow.ellipsis,
              style: TextStyles.common(
                  fontSize ?? 14.sp, textColor ?? AppColors.colorFF666666, h: 1.3),
            ),
            Visibility(
              visible: editable == true,
              child: Padding(
                padding: EdgeInsets.only(left: 10.w),
                child: GestureDetector(
                  onTap: deleteAction,
                  child: Icon(
                    Icons.close,
                    size: closeBtnSize ?? 12.w,
                    color: closeBtnColor ?? textColor,
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
