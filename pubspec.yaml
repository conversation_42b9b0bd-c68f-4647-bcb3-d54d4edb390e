name: dada
description: "A new Flutter project."
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
#publish_to: "none" # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 1.0.3+2025051502

environment:
  sdk: ">=3.3.1 <4.0.0"

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter

  flutter_localizations:
    sdk: flutter

  # GetX
  get: ^4.6.6

  # Api request
  dio: ^5.4.1
  pretty_dio_logger: ^1.3.1

  # 下拉刷新 和 上拉加载更多
  easy_refresh: ^3.3.4

  # loading
  flutter_easyloading: ^3.0.5

  # 数据存储
  shared_preferences: ^2.2.2
  sp_util: ^2.0.3

  # 网络图片加载
  cached_network_image: ^3.4.1

  # 事件总线
  event_bus: ^2.0.1

  # WebView
  flutter_inappwebview: ^6.1.5

  # 语言国际化
  intl: ^0.18.0
  intl_utils: ^2.8.4

  #屏幕适配
  flutter_screenutil: ^5.9.0

  # SVGA Player
  svgaplayer_flutter: ^2.2.0

  # 悬浮视图
  flutter_floating: ^1.0.8

  # 悬浮窗插件等
  overlay_support: ^2.1.0

  # logger
  logger: ^2.0.1

  # 数据库
  sqflite: ^2.3.3+1

  # 路径工具类
  path: ^1.9.0
  path_provider: ^2.1.4

  # 按需加载的PageView
  lazy_load_indexed_stack: ^1.1.0

  # 相册图片获取
  image_picker: ^0.8.5+3
  cross_file: ^0.3.3+4

  wechat_assets_picker: ^9.3.2
  wechat_camera_picker: ^4.3.2

  # 时间格式化
  date_format: ^2.0.5

  # 图片预览
  photo_view: ^0.15.0

  # 录音及播放
  flutter_sound: ^9.16.3
  just_audio: ^0.9.40

  # 权限管理
  permission_handler: ^11.3.1

  # 语音事务设置
  audio_session: ^0.1.21

  # ota下载
  ota_update: ^6.0.0

  # 可展开文本
  expandable_text: ^2.3.0

  # 视频播放
  video_player: ^2.9.2
  # 视频播放缓存管理器
  cached_video_player_plus: ^3.0.3

  # 提示气泡UI
  tolyui: ^0.0.2+21

  # The following adds the Cupertino Icons font to your application.
  # Use with the CupertinoIcons class for iOS style icons.
  cupertino_icons: ^1.0.6

  # 腾讯云 RTC
  tencent_trtc_cloud: ^2.9.4
  # 腾讯云 IM
  tencent_cloud_chat_sdk: ^8.3.6498
  # 腾讯云 Push
  tencent_cloud_chat_push: ^8.3.6498+1

  # 拼音识别
  pinyin: ^3.3.0

  # 列表滑动组件（左滑删除）
  flutter_slidable: ^3.1.1

  # 弹幕组件
  flutter_infinite_marquee: ^1.0.0

  # 跑马灯滚动文字组件
  marquee: ^2.2.3

#  # 网络连接状态监测
#  connectivity_plus: ^6.1.0

  # 轮播视图
  carousel_slider: ^5.0.0

  # 动画库
  animate_do: ^3.3.4

  # rxdart
  rxdart: ^0.28.0

  # 微信支付
  wechat_kit: ^6.0.2

  wechat_kit_extension: ^1.0.1
  # 支付宝支付
  alipay_kit: ^6.0.0

  # 苹果支付
  in_app_purchase: ^3.2.0

  # 打开网页或设置
  url_launcher: ^6.3.1

  # 锁屏
  wakelock_plus: ^1.1.6

  # 图片压缩
  flutter_image_compress: ^2.4.0
  # 视频压缩
  video_compress: ^3.1.3

  # 数字选择器
  numberpicker: ^2.1.2 # 请使用最新版本

  # 城市选择
  city_pickers: ^1.1.0

  # 缓存管理器
  flutter_cache_manager: ^3.4.1

  # 系统代理
  system_proxy: ^0.1.0
#
#  # 安卓后台保活
#  flutter_foreground_task: ^8.17.0

  # 获取包信息
  package_info_plus: ^8.3.0

  # 启动页
  flutter_native_splash: ^2.2.16

  # 腾讯云聊天UI框架
  tencent_cloud_chat_uikit:
    path: ../pubspec/dada/tencent_cloud_chat_uikit-2.7.2
  tim_ui_kit_sticker_plugin:
    path: ../pubspec/dada/tim_ui_kit_sticker_plugin-3.2.0

  # 滚动弹幕插件
  flutter_barrage_craft:
    path: ../pubspec/dada/flutter_barrage_craft-1.0.1

  # 点9图显示
  ninepatch_image:
    path: ../pubspec/dada/ninepatch_image-0.0.5

  # pangle广告聚合插件
  flutter_gromore_ads:
      path: ../pubspec/dada/flutter_gromore_ads-3.7.5
  dotted_border: ^2.1.0

dependency_overrides:
  vm_service: ^13.0.0
  dio: ^5.4.1
  http: ^1.2.2
  intl: ^0.18.0
  uuid: ^4.3.3
  file: ^7.0.0
  flutter_sound: ^9.16.3
  js: ^0.7.1
  flutter_image_compress: ^2.3.0
  image_clipboard: ^1.1.0+1
  csslib: ^1.0.2
  extended_text: ^14.2.0
  extended_text_field: ^16.0.1
  fading_edge_scrollview: ^4.1.1
  package_info_plus: ^8.3.0
  # 文字详细简介气泡、点击浮窗等控件
  tolyui_feedback:
    path: ../pubspec/dada/tolyui_feedback-0.3.4+7
  tolyui_navigation:
    path: ../pubspec/dada/tolyui_navigation-0.1.0

dev_dependencies:
  flutter_test:
    sdk: flutter

  # The "flutter_lints" package below contains a set of recommended lints to
  # encourage good coding practices. The lint set provided by the package is
  # activated in the `analysis_options.yaml` file located at the root of your
  # package. See that file for information about deactivating specific lint
  # rules and activating additional ones.
  flutter_lints: ^3.0.0

  # Flutter_ume 调试工具
  flutter_ume: ^1.0.1
  flutter_ume_kit_device: ^1.0.0
  flutter_ume_kit_perf: ^1.0.0
  flutter_ume_kit_show_code: ^1.0.0
  flutter_ume_kit_console: ^1.0.0
  flutter_ume_kit_dio:
    path: ../pubspec/dada/flutter_ume_kit_dio-1.0.1+2
  flutter_ume_kit_ui:
    path: ../pubspec/dada/flutter_ume_kit_ui-1.1.0

  flutter_launcher_icons: ^0.11.0
  flutter_ume_kit_mock:
    git:
      url: https://gitee.com/flutter-mirror/flutter_ume_kit_mock.git
      ref: 1.0.5

flutter_icons:
  android: true
  ios: true
  image_path: "assets/images/app_icon.png"
  remove_alpha_ios: true

flutter_native_splash:
  # 设置启动图背景颜色
  color: "#FFFFFF" # 背景颜色（十六进制格式）

  # 设置启动图图片（支持本地图片）
  image: assets/images/splash.png # 图片路径（放在 assets 文件夹中）

  # 设置图片填充方式（可选）
  image_loading: true # 图片是否居中显示
  full_screen: true # 是否全屏显示
  android: true # 启用 Android 平台
  ios: true # 启用 iOS 平台

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:
  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true
  assets:
    - assets/images/
    - assets/svga/
    - assets/svga/dress/
    - assets/custom_face_resource/dada_default_emojis/

  fonts:
    - family: YouSheBiaoTiHei
      fonts:
        - asset: assets/fonts/YouSheBiaoTiHei.ttf

flutter_assets_generator:
  path_ignore:
    - assets/fonts/
    - assets/svga/dress/
    - assets/custom_face_resource/

  # To add assets to your application, add an assets section, like this:
  # assets:
  #   - images/a_dot_burr.jpeg
  #   - images/a_dot_ham.jpeg

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/assets-and-images/#resolution-aware

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/assets-and-images/#from-packages

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  # fonts:
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/custom-fonts/#from-packages
flutter_intl:
  enabled: true

wechat_kit:
  ios: no_pay
  app_id: wxa77c84705ceddf4c
  universal_link: https://daya.com
alipay_kit:
  scheme: com.daya.app
