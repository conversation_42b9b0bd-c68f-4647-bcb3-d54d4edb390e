<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    tools:ignore="ExtraText">
    >

    <uses-feature
        android:name="android.hardware.camera"
        android:required="false"
        tools:targetApi="eclair" />
    <application
        android:name="com.daya.app.MyApplication"
        android:icon="@mipmap/ic_launcher"
        android:label="搭吖"
        android:allowBackup="true"
        android:usesCleartextTraffic="true"
        android:requestLegacyExternalStorage="true"
        android:networkSecurityConfig="@xml/network_config"
        android:enableOnBackInvokedCallback="true"
        android:stopWithTask="true"
        tools:replace="android:networkSecurityConfig,label,icon,allowBackup">

        <uses-library android:name="org.apache.http.legacy" android:required="false"/>

        <activity
            android:name=".MainActivity"
            android:configChanges="orientation|keyboardHidden|keyboard|screenSize|smallestScreenSize|locale|layoutDirection|fontScale|screenLayout|density|uiMode"
            android:exported="true"
            android:hardwareAccelerated="true"
            android:launchMode="singleTop"
            android:theme="@style/LaunchTheme"
            android:windowSoftInputMode="adjustResize">
            <!-- Specifies an Android theme to apply to this Activity as soon as
                 the Android process has started. This theme is visible to the user
                 while the Flutter UI initializes. After that, this theme continues
                 to determine the Window background behind the Flutter UI. -->
            <meta-data
                android:name="io.flutter.embedding.android.NormalTheme"
                android:resource="@style/NormalTheme" />
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>
        <!-- Don't delete the meta-data below.
             This is used by the Flutter tool to generate GeneratedPluginRegistrant.java -->
        <meta-data
            android:name="flutterEmbedding"
            android:value="2" />

    </application>
    <!-- Required to query activities that can process text, see:
         https://developer.android.com/training/package-visibility?hl=en and
         https://developer.android.com/reference/android/content/Intent#ACTION_PROCESS_TEXT.

         In particular, this is used by the Flutter engine in io.flutter.plugin.text.ProcessTextPlugin. -->
    <queries>
        <intent>
            <action android:name="android.intent.action.PROCESS_TEXT" />
            <data android:mimeType="text/plain" />
        </intent>
    </queries>


    <!--网络权限-->
    <uses-permission android:name="android.permission.INTERNET"
        tools:ignore="ManifestOrder" />
    <!--获取手机网络状态权限-->
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE"/>

    <!--相机权限-->
    <uses-permission android:name="android.permission.CAMERA" />

    <!--媒体访问权限-->
    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES" />
    <uses-permission android:name="android.permission.READ_MEDIA_VIDEO" />
    <uses-permission android:name="android.permission.READ_MEDIA_AUDIO" />
    <uses-permission android:name="android.permission.ACCESS_MEDIA_LOCATION" />

    <!--通知权限-->
    <uses-permission android:name="android.permission.POST_NOTIFICATIONS"/>

    <!--用于申请调用麦克风录音-->
    <uses-permission android:name="android.permission.RECORD_AUDIO" />

    <!--用于防止在后台运行进入休眠模式-->
    <uses-permission android:name="android.permission.WAKE_LOCK" />

    <!--允许应用访问设备的存储，用于写入文件-->
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />

    <!--允许应用访问设备的存储，用于读取文件-->
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />

    <!--删除穿山甲获取手机应用列表权限-->
    <uses-permission android:name="android.permission.REQUEST_INSTALL_PACKAGES" tools:node="remove"/>
    <!--删除穿山甲获取手机状态权限-->
    <uses-permission android:name="android.permission.READ_PHONE_STATE" tools:node="remove"/>
    <!--删除穿山甲获取手机位置权限-->
    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" tools:node="remove"/>
    <!--删除穿山甲获取手机直播悬浮窗口权限-->
    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" tools:node="remove"/>
    <!--删除穿山甲获取手机应用列表权限-->
    <uses-permission android:name="android.permission.QUERY_ALL_PACKAGES" tools:node="remove"/>


</manifest>
